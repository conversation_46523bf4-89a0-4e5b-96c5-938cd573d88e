<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ProductDiscount;
use App\Models\Product;
use App\Http\Requests\ProductDiscountRequest;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use DB;
class ProductDiscountsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
         $this->middleware('permission:productdiscounts-list|productdiscounts-create|productdiscounts-edit|productdiscounts-delete', ['only' => ['index','store']]);
         $this->middleware('permission:productdiscounts-create', ['only' => ['create','store']]);
         $this->middleware('permission:productdiscounts-edit', ['only' => ['edit','update']]);
         $this->middleware('permission:productdiscounts-delete', ['only' => ['destroy']]);
         $this->middleware('permission:productdiscounts-list', ['only' => ['show']]);

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        // Get discount-specific statistics
        $userId = auth()->id();
        $isAdmin = auth()->user()->hasRole('admin');

        // Base query for role-based filtering
        $baseQuery = ProductDiscount::query();
        if (!$isAdmin) {
            $baseQuery->whereHas('product', function($q) use ($userId) {
                $q->where('user_id', $userId);
            });
        }

        // All products that have ever had discounts
        $allDiscountedProducts = (clone $baseQuery)->distinct('product_id')->count();

        // Products with currently active discounts
        $activeDiscountedProducts = (clone $baseQuery)->where('status', 1)
            ->whereDate('start_date', '<=', now())
            ->whereDate('end_date', '>=', now())
            ->distinct('product_id')
            ->count();

        // Products with expired discounts
        $expiredDiscounts = (clone $baseQuery)->where('status', 1)
            ->whereDate('end_date', '<', now())
            ->distinct('product_id')
            ->count();

        // Products with upcoming discounts (future start date)
        $upcomingDiscounts = (clone $baseQuery)->where('status', 1)
            ->whereDate('start_date', '>', now())
            ->distinct('product_id')
            ->count();

        return view('productdiscounts.index', [
            'allDiscountedProducts' => $allDiscountedProducts,
            'activeDiscountedProducts' => $activeDiscountedProducts,
            'expiredDiscounts' => $expiredDiscounts,
            'upcomingDiscounts' => $upcomingDiscounts
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('productdiscounts.create');
    }



    /**
     * Store a newly created resource in storage.
     *
     * @param  ProductDiscountRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(ProductDiscountRequest $request)
    {
        $request->validate([
            'discount_percentage' => 'required',
            'start_date' => 'required',
            'end_date' => 'required',
            'product_ids' => 'required|array|min:1',
            'discount_quantities' => 'required|array',
        ]);


        try {
            DB::beginTransaction();

            foreach ($request->product_ids as $productId) {
                $quantity = $request->discount_quantities[$productId] ?? null;

                if (!$quantity || $quantity <= 0) {
                    throw new \Exception("Invalid quantity for product ID {$productId}");
                }

                ProductDiscount::create([
                    'product_id' => $productId,
                    'discount_percentage' => $request->discount_percentage,
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'status' => 1,
                    'avail_quantity' => $quantity,
                ]);
            }

            DB::commit();
            return redierct(url('products-discount'))->with(['title'=>'Done', 'type'=>'success', 'message'=>'Discount(s) applied successfully.']);

            return redirect()->back()->with(['title'=>'Done', 'type'=>'success', 'message'=>'Discount(s) applied successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Product Discount Error: ' . $e->getMessage());
            return redirect()->back()->with(['title'=>'error', 'type'=>'Error', 'message'=>'Failed to apply discount. Please try again.']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $discount = ProductDiscount::with(['product.image', 'product.category'])->findOrFail($id);
        return view('productdiscounts.show', compact('discount'));
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $discount = ProductDiscount::with(['product.image', 'product.category'])->findOrFail($id);
        return view('productdiscounts.edit', compact('discount'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  ProductDiscountRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        $request->validate([
            'discount_percentage' => 'required|numeric|min:1|max:100',
            'start_date' => 'required|date',
            'end_date' => 'required|date|after:start_date',
            'discount_quantity' => 'nullable|numeric|min:1',
        ]);

        $discount = ProductDiscount::findOrFail($id);
        $discount->discount_percentage = $request->input('discount_percentage');
        $discount->start_date = $request->input('start_date');
        $discount->end_date = $request->input('end_date');
        $discount->discount_quantity = $request->input('discount_quantity');
        $discount->save();

        return redirect()->route('products-discount.show', $discount->id)
                        ->with('success', 'Discount updated successfully!');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $productdiscount = ProductDiscount::findOrFail($id);
        $productdiscount->delete();

        return to_route('productdiscounts.index');
    }

    /**
     * Get table data for DataTables AJAX
     */
    public function getTableData(Request $request)
    {
        $userId = auth()->id();
         $isAdmin = auth()->user()->hasRole('admin');

        // Base query for products with active discounts
        $query = Product::with(['activeDiscount', 'category', 'seller', 'variants'])
            ->whereHas('productDiscount', function($q) {
                $q->where('status', 1)
                  ->whereDate('start_date', '<=', now())
                  ->whereDate('end_date', '>=', now());
            });

        // Apply role-based filtering
        if (!$isAdmin) {
            $query->where('user_id', $userId);
        }

        // Apply search if provided
        if ($request->has('search') && !empty($request->search['value'])) {
            $search = $request->search['value'];
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku_id', 'like', "%{$search}%")
                  ->orWhereHas('category', function($categoryQuery) use ($search) {
                      $categoryQuery->where('name', 'like', "%{$search}%");
                  });
            });
        }

        // Get total count before pagination
        $totalRecords = $query->count();

        // Apply ordering
        if ($request->has('order')) {
            $orderColumn = $request->order[0]['column'];
            $orderDirection = $request->order[0]['dir'];

            $columns = ['sku_id', 'name', 'price', 'discount', 'stock', 'category'];
            if (isset($columns[$orderColumn])) {
                if ($columns[$orderColumn] === 'category') {
                    $query->join('categories', 'products.category_id', '=', 'categories.id')
                          ->orderBy('categories.name', $orderDirection)
                          ->select('products.*');
                } elseif ($columns[$orderColumn] === 'discount') {
                    // Skip ordering by discount as it's calculated
                    $query->orderBy('created_at', 'desc');
                } else {
                    $query->orderBy($columns[$orderColumn], $orderDirection);
                }
            }
        }

        // Apply pagination
        $start = $request->start ?? 0;
        $length = $request->length ?? 10;
        $products = $query->skip($start)->take($length)->get();

        // Format data for DataTable
        $data = [];
        foreach ($products as $product) {
            $totalStock = $product->variants->sum('stock');
            $activeDiscount = $product->activeDiscount;

            // Determine discount status
            $status = 'N/A';
            $statusClass = 'bg-secondary';
            if ($activeDiscount) {
                $now = now();
                $startDate = \Carbon\Carbon::parse($activeDiscount->start_date);
                $endDate = \Carbon\Carbon::parse($activeDiscount->end_date);

                if ($now->lt($startDate)) {
                    $status = 'Upcoming';
                    $statusClass = 'bg-info';
                } elseif ($now->between($startDate, $endDate)) {
                    $status = 'Active';
                    $statusClass = 'bg-success';
                } else {
                    $status = 'Expired';
                    $statusClass = 'bg-danger';
                }
            }

            $data[] = [
                'product' => '<div class="product_name_wrapper">
                            <div class="product_img">
                                <img src="' . ($product->image ? asset('website/' . $product->image->image) : asset('website/assets/images/no-image.png')) . '" alt="Product Image" style="width: 40px; height: 40px; object-fit: cover; border-radius: 4px;">
                            </div>
                            <div class="product_name_details">
                                <h6 style="margin: 0; font-size: 14px; font-weight: 500;">' . substr($product->name, 0, 30) . (strlen($product->name) > 30 ? '...' : '') . '</h6>
                                <p style="margin: 0; font-size: 12px; color: #6c757d;">SKU: ' . ($product->sku_id ?? 'N/A') . '</p>
                            </div>
                        </div>',
                'price' => '£' . number_format($product->price, 2),
                'discount' => $activeDiscount ? '<span class="badge bg-success">' . $activeDiscount->discount_percentage . '%</span>' : '<span class="badge bg-secondary">No Discount</span>',
                'start_date' => $activeDiscount ? date('d M Y', strtotime($activeDiscount->start_date)) : 'N/A',
                'end_date' => $activeDiscount ? date('d M Y', strtotime($activeDiscount->end_date)) : 'N/A',
                'status' => '<span class="badge ' . $statusClass . '">' . $status . '</span>',
                'stock' => $totalStock,
                'category' => $product->category->name ?? 'N/A',
                'actions' => '<div class="table_view_ban_icon_wrapper">
                                <a href="' . route('products-discount.show', $activeDiscount ? $activeDiscount->id : $product->id) . '" class="btn btn-info btn-xs" title="View Discount">
                                    <i class="fa fa-eye"></i>
                                </a>
                                <a href="' . route('products-discount.edit', $activeDiscount ? $activeDiscount->id : $product->id) . '" class="btn btn-warning btn-xs" title="Edit Discount">
                                    <i class="fa fa-edit"></i>
                                </a>
                            </div>'
            ];
        }

        return response()->json([
            'draw' => intval($request->draw),
            'recordsTotal' => $totalRecords,
            'recordsFiltered' => $totalRecords,
            'data' => $data
        ]);
    }

    /**
     * Get products for discount modal/page
     */
    public function getProducts(Request $request)
    {
        $userId = auth()->id();
        $isAdmin = auth()->user()->hasRole('admin');

        $query = Product::with(['category', 'variants']);

        // Apply role-based filtering
        if (!$isAdmin) {
            $query->where('user_id', $userId);
        }

        // Apply search filter
        if ($request->has('search') && !empty($request->search)) {
            $search = $request->search;
            $query->where(function($q) use ($search) {
                $q->where('name', 'like', "%{$search}%")
                  ->orWhere('sku_id', 'like', "%{$search}%");
            });
        }

        // Pagination
        $perPage = 10;
        $page = $request->get('page', 1);
        $products = $query->paginate($perPage, ['*'], 'page', $page);

        $data = [];
        foreach ($products as $product) {
            // Calculate total stock from variants
            $totalStock = $product->variants->sum('stock');

            $data[] = [
                'id' => $product->id,
                'name' => substr($product->name, 0, 50) . (strlen($product->name) > 50 ? '...' : ''),
                'sku_id' => $product->sku_id ?? 'N/A',
                'price' => number_format($product->price, 2),
                'stock' => $totalStock,
                'image' => $product->image ? asset('website/' . $product->image->image) : asset('website/assets/images/no-image.png')
            ];
        }

        return response()->json([
            'data' => $data,
            'has_more_pages' => $products->hasMorePages(),
            'current_page' => $products->currentPage(),
            'total' => $products->total()
        ]);
    }
}
