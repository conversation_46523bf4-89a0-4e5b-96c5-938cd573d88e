<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\ProductDiscount;
use App\Http\Requests\ProductDiscountRequest;
use Spatie\Permission\Models\Permission;
use DB;
class ProductDiscountsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
         $this->middleware('permission:productdiscounts-list|productdiscounts-create|productdiscounts-edit|productdiscounts-delete', ['only' => ['index','store']]);
         $this->middleware('permission:productdiscounts-create', ['only' => ['create','store']]);
         $this->middleware('permission:productdiscounts-edit', ['only' => ['edit','update']]);
         $this->middleware('permission:productdiscounts-delete', ['only' => ['destroy']]);
         $this->middleware('permission:productdiscounts-list', ['only' => ['show']]);

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {
        $productDiscounts= ProductDiscount::all();
        return view('productdiscounts.index', ['productDiscounts'=>$productDiscounts]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        return view('productdiscounts.create');
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  ProductDiscountRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(ProductDiscountRequest $request)
    {
        $request->validate([
            'discount_percentage' => 'required|numeric',
            'start_date' => 'required',
            'end_date' => 'required',
            'product_id' => 'required|array|min:1',
            'avail_quantity' => 'required|array',
        ]);


        try {
            DB::beginTransaction();

            foreach ($request->product_id as $productId) {
                $quantity = $request->avail_quantity[$productId] ?? null;

                if (!$quantity || $quantity <= 0) {
                    throw new \Exception("Invalid quantity for product ID {$productId}");
                }

                ProductDiscount::create([
                    'product_id' => $productId,
                    'discount_percentage' => $request->discount_percentage,
                    'start_date' => $request->start_date,
                    'end_date' => $request->end_date,
                    'status' => 1,
                    'avail_quantity' => $quantity,
                ]);
            }

            DB::commit();

            return redirect()->back()->with(['title'=>'Done', 'type'=>'success', 'message'=>'Discount(s) applied successfully.']);
        } catch (\Exception $e) {
            DB::rollBack();
            Log::error('Product Discount Error: ' . $e->getMessage());
            return redirect()->back()->with(['title'=>'error', 'type'=>'Error', 'message'=>'Failed to apply discount. Please try again.']);
        }
    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        $productdiscount = ProductDiscount::findOrFail($id);
        return view('productdiscounts.show',['productdiscount'=>$productdiscount]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        $productdiscount = ProductDiscount::findOrFail($id);
        return view('productdiscounts.edit',['productdiscount'=>$productdiscount]);
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  ProductDiscountRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(ProductDiscountRequest $request, $id)
    {
        $productdiscount = ProductDiscount::findOrFail($id);
		$productdiscount->product_id = $request->input('product_id');
		$productdiscount->discount_percentage = $request->input('discount_percentage');
		$productdiscount->start_date = $request->input('start_date');
		$productdiscount->end_date = $request->input('end_date');
		$productdiscount->status = $request->input('status');
		$productdiscount->avail_quantity = $request->input('avail_quantity');
        $productdiscount->save();

        return to_route('productdiscounts.index');
    }

    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $productdiscount = ProductDiscount::findOrFail($id);
        $productdiscount->delete();

        return to_route('productdiscounts.index');
    }
}
