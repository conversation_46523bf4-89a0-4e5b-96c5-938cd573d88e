<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;

use App\Models\Product;
use App\Models\Category;
use App\Http\Requests\ProductRequest;
use App\Models\ProductDiscount;
use App\Models\ProductImage;
use App\Models\ProductVariant;
use App\Models\Size;
use Spatie\Permission\Models\Permission;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\ProductAttribute;
use Illuminate\Support\Facades\Storage;
use Auth;
class ProductsController extends Controller
{

    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Http\Response
     */
    function __construct()
    {
         $this->middleware('permission:products-list|products-create|products-edit|products-delete', ['only' => ['index','store']]);
         $this->middleware('permission:products-create', ['only' => ['create','store']]);
         $this->middleware('permission:products-edit', ['only' => ['edit','update']]);
         $this->middleware('permission:products-delete', ['only' => ['destroy']]);
         $this->middleware('permission:products-list', ['only' => ['show']]);

    }
    /**
     * Display a listing of the resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function index()
    {

        // Get counts for dashboard cards
        if (auth()->user()->hasRole('admin')){
            $totalProductsCount = Product::count();
            $activeProductsCount = Product::where('status', 1)->count();
        }else{
            $totalProductsCount = Product::where('user_id', auth()->user()->id)->count();
            $activeProductsCount = Product::where('user_id', auth()->user()->id)->where('status', 1)->count();
        }

        $outOfStockCount = Product::where('user_id', auth()->id())->whereHas('variants', function ($query) {
                $query->select(DB::raw('SUM(stock) as total_stock'))
                    ->groupBy('product_id')
                    ->havingRaw('SUM(stock) = 0');
            })->count();

        return view('products.index', [
            'totalProductsCount' => $totalProductsCount,
            'activeProductsCount' => $activeProductsCount,
            'outOfStockCount' => $outOfStockCount
        ]);
    }

    /**
     * Show the form for creating a new resource.
     *
     * @return \Illuminate\Contracts\View\View
     */
    public function create()
    {
        $categories= Category::where('status',1)->get();
        $sizes =Size::where('status',1)->get();
        return view('products.create',compact('categories','sizes'));
    }

    /**
     * Store a newly created resource in storage.
     *
     * @param  ProductRequest  $request
     * @return \Illuminate\Http\RedirectResponse
     */
    public function store(ProductRequest $request)
    {
        extract($request->all());
        try {
            DB::beginTransaction();
            if (isset($shipping_fee)){
                $shipping_fee = 0;
            }else{
                $shipping_fee = 1;
            }

            if(isset($sizes) && $sizes!=null){
                $sizes = json_encode($sizes);
            }else{
                $sizes = null;
            }
            if(isset($colors) && $colors!=null){
                $colors = json_encode($colors);
            }else{
                $colors = null;
            }

            $product = Product::create([
                'name' => $name,
                'sku_id' => $sku_id,
                'category_id' => $category_id,
                'description' => $description,
                'shipping_fee' => $shipping_fee??"",
                'heading' => $heading??"variants",
                'status' => $status,
                'user_id'=>auth()->user()->id,
                'sizes' => $sizes,
                'colors' => $colors,
                'unique' => time()
            ]);
            if (isset($has_variants)){
                foreach ($variants as $key=>$variant)    {
                    ProductVariant::create(
                        [
                            'product_id' => $product->id,
                            'name' => $variant['name'],
                            'value' => $variant['value'],
                            'price' => $variant['price'],
                            'stock' => $variant['stock'],
                        ]
                    );

                }
            }
            if (isset($has_attributes)){
                foreach ($attributes as $key=>$attribute)    {
                    ProductAttribute::create(
                        [
                            'product_id' => $product->id,
                            'name' => $attribute['name'],
                            'value' => $attribute['value']
                        ]
                    );

                }
            }


            ProductImage::where('timestamp', $timestamp)->update(['product_id' => $product->id]);

            /*if (isset($product_discount_percentage)){
                ProductDiscount::create([
                    'product_id' => $product->id,
                    'discount_percentage' => $product_discount_percentage,
                    'start_date' => $start_date,
                    'end_date' => $end_date,
                    'status' => 1,
                ]);
            }*/
            DB::commit();
            return to_route('products.index')->with(['message' => 'Product created successfully.','title'=>'Success','icon'=>'success']);

        }catch (Exception $e){
            DB::rollBack();
            return to_route('products.index')->with(['message' => 'Unable to create product.','title'=>'Error','icon'=>'error']);
        }

    }

    /**
     * Display the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function show($id)
    {
        if(Auth::user()->hasRole('admin')){
            $product = Product::where('unique', $id)->firstOrFail();
        }else{
//            $product = Product::where('user_id',auth()->user()->id)->findOrFail($id);
            $product = Product::where('unique', $id)->firstOrFail();
        }
        return view('products.show',['product'=>$product]);
    }

    /**
     * Show the form for editing the specified resource.
     *
     * @param  int  $id
     * @return \Illuminate\Contracts\View\View
     */
    public function edit($id)
    {
        if(Auth::user()->hasRole('admin')){
//            $product = Product::findOrFail($id);
            $product = Product::where('unique', $id)->firstOrFail();
        }else{
            $product = Product::where('user_id',auth()->user()->id)->where('unique', $id)->firstOrFail();
        }
        $categories = Category::where('status', 1)->get();
        $sizes = Size::where('status', 1)->get();
         $existingImages = $product->images->map(function ($image) {
            return [
                'id' => $image->id,
                'url' => asset('website/' . $image->image),
                'name' => basename($image->image),
                'size' => @filesize($_SERVER['DOCUMENT_ROOT'].'/'.('website/' . $image->image)),
                'isPrimary' => $image->is_primary??0,
            ];
        });
        return view('products.edit', compact('product', 'categories', 'sizes', 'existingImages'));
    }

    /**
     * Update the specified resource in storage.
     *
     * @param  ProductRequest  $request
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function update(Request $request, $id)
    {
        extract($request->all());
        try {
            DB::beginTransaction();
            if(isset($remove_attribute) && $remove_attribute!=null){
                ProductAttribute::whereIn('id',$remove_attribute)->delete();
            }

            if(isset($remove_variant) && $remove_variant!=null){
                ProductVariant::whereIn('id',$remove_variant)->delete();
            }
//        $product = Product::findOrFail($id);
            $product = Product::where('unique', $id)->firstOrFail();
            $shipping_fee = $request->has('shipping_fee') ? 1 : 0;
            $sizes = $request->has('sizes') ? json_encode($request->sizes) : null;
            $colors = $request->has('colors') ? json_encode($request->colors) : null;

            $product->update([
                'name' => $request->name,
                'sku_id' => $request->sku_id,
                'category_id' => $request->category_id,
                'description' => $request->description,
                'heading' => $request->heading,
                'shipping_fee' => $shipping_fee,
                'status' => $request->status,
                'sizes' => $sizes,
                'colors' => $colors,
            ]);

            if ($request->has('has_variants')) {
                foreach ($request->variants as $variant) {
                    if ( $variant['name'] !== null && $variant['value'] !== null && $variant['price'] !== null && $variant['stock'] !== null) {
                        if (isset($variant['id'])){
                            ProductVariant::where('id',$variant['id'])->Update([
                                'product_id' => $product->id,
                                'name'       => $variant['name'],
                                'value'      => $variant['value'],
                                'price'      => $variant['price'],
                                'stock'      => $variant['stock'],
                            ]);
                        } else {
                    ProductVariant::create([
                        'product_id' => $product->id,
                        'name' => $variant['name'],
                        'value' => $variant['value'],
                        'price' => $variant['price'],
                        'stock' => $variant['stock'],
                    ]);
                }
            }
                }
            }

//            if ($request->has('has_attributes')) {
                if (isset($request['attributes'])){
                    foreach ($request['attributes'] as $attribute) {
                        if (isset($attribute['id'])){
                            ProductAttribute::where('id',$attribute['id'])->update([
                        'product_id' => $product->id,
                        'name' => $attribute['name'],
                        'value' => $attribute['value'],
                    ]);
            }else{
                            ProductAttribute::create([
                                'product_id' => $product->id,
                        'name' => $attribute['name'],
                        'value' => $attribute['value'],
                    ]);
                }
            }
                }
//            }

            ProductImage::where('timestamp', $request->timestamp)->update(['product_id' => $product->id]);

            DB::commit();
            return redirect()->back()->with([
                'message' => 'Product updated successfully.',
                'title' => 'Success',
                'icon' => 'success',
            ]);

        } catch (Exception $e) {
            DB::rollBack();
            return redirect()->back()->with([
                'message' => 'Unable to update product.',
                'title' => 'Error',
                'icon' => 'error',
            ]);
    }
    }



    /**
     * Remove the specified resource from storage.
     *
     * @param  int  $id
     * @return \Illuminate\Http\RedirectResponse
     */
    public function destroy($id)
    {
        $product = Product::where('unique', $id)->firstOrFail();
        $product->images()->delete();
        $product->variants()->delete();
        $product->attributes()->delete();
        $product->productDiscount()->delete();
        $product->delete();
        return to_route('products.index')->with(['title'=>'Done!', 'type'=>'success', 'message'=>'Product deleted successfully']);
    }

    public function getTableData(Request $request)
    {
        try {
            $start = $request->input('start');
            $length = $request->input('length');
            $searchValue = $request->input('search.value');
            $draw = $request->input('draw');

            // Base query with role-based filtering
            $user = auth()->user();
            if ($user && method_exists($user, 'hasRole') && $user->hasRole('admin')){
                $query = Product::with(['category', 'seller']);
            } else {
                $query = Product::with(['category', 'seller'])->where('user_id', $user->id);
            }

        $total = $query->count(); // total before filtering

        // Apply search filter
        if (!empty($searchValue)) {
            $query->where(function($q) use ($searchValue) {
                $q->where('name', 'like', '%' . $searchValue . '%')
                  ->orWhere('sku_id', 'like', '%' . $searchValue . '%')
                  ->orWhereHas('category', function($categoryQuery) use ($searchValue) {
                      $categoryQuery->where('name', 'like', '%' . $searchValue . '%');
                  })
                  ->orWhereHas('seller', function($sellerQuery) use ($searchValue) {
                      $sellerQuery->where('name', 'like', '%' . $searchValue . '%');
                  });
            });
        }

        $filtered = $query->count(); // count after search filter

        // Apply pagination
        $products = $query->orderBy('sorting', 'ASC')
            ->skip($start)
            ->take($length)
            ->get();

        $data = [];

        foreach ($products as $product) {
            $data[] = [
                'sku_id' => $product->sku_id ?? 'N/A',
                'name' => $product->shortName ?? 'N/A',
                'price' => env('ACTIVE_CURRENCY_SYMBOL') . ($product->price ?? 0),
                'stock' => $product->total_stock ?? 0,
                'status' => $product->checkStatus ?? 'N/A',
                'is_featured' => $product->checkFeatured ?? 'N/A',
                'category' => optional($product->category)->name ?? 'N/A',
                'seller' => ucwords(optional($product->seller)->name ?? 'N/A'),
                'date_added' => $product->created_at ? $product->created_at->format(env('GLOBAL_DATE_FORMAT') ?? 'd-m-Y') : 'N/A',
                'actions' => '
                <div class="table_view_ban_icon_wrapper">
                    <a href="' . route('products.show', [$product->unique]) . '" class="table_td_veiw_icon btn btn-xs btn-info" style="padding: 4px 8px; font-size: 12px;"><i class="fa-regular fa-eye"></i></a>
                    <a href="' . route('products.edit', [$product->unique]) . '" class="table_td_veiw_icon btn btn-xs btn-warning" style="padding: 4px 8px; font-size: 12px;"><i class="fa-regular fa-pen-to-square"></i></a>
                    <form method="POST" action="' . route('products.destroy', $product->unique) . '" class="delete-form" style="display:inline;">
                        ' . csrf_field() . method_field('DELETE') . '
                        <a href="javascript:void(0);" onclick="showDeleteConfirmation(this)" class="table_td_veiw_icon btn btn-xs btn-danger" style="padding: 4px 8px; font-size: 12px;">
                            <i class="fa-regular fa-trash-can"></i>
                        </a>
                    </form>
                </div>',
            ];
        }

        return response()->json([
            'draw' => intval($draw),
            'recordsTotal' => $total,
            'recordsFiltered' => $filtered,
            'data' => $data,
        ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error loading products: ' . $e->getMessage(),
                'draw' => intval($draw),
                'recordsTotal' => 0,
                'recordsFiltered' => 0,
                'data' => []
            ], 500);
        }
    }

    public function getDiscountModalProducts(Request $request)
    {
        try {
            $search = $request->get('search', '');
            $page = $request->get('page', 1);
            $perPage = 10; // Load only 10 products at a time

            // Base query with role-based filtering
            $user = auth()->user();
            if ($user && method_exists($user, 'hasRole') && $user->hasRole('admin')){
                $query = Product::with('category');
            } else {
                $query = Product::with('category')->where('user_id', $user->id);
            }

            // Apply search if provided
            if (!empty($search)) {
                $query->where(function($q) use ($search) {
                    $q->where('name', 'like', '%' . $search . '%')
                      ->orWhere('sku_id', 'like', '%' . $search . '%');
                });
            }

            $products = $query->orderBy('sorting', 'ASC')
                ->paginate($perPage, ['*'], 'page', $page);

            $html = '';
            foreach ($products as $product) {
                $html .= '
                    <tr>
                        <td>
                            <input type="checkbox" class="form-check-input select_product" name="product_id[]" value="' . $product->id . '">
                        </td>
                        <td>' . ucwords($product->shortName ?? $product->name ?? 'N/A') . '</td>
                        <td>' . env('ACTIVE_CURRENCY_SYMBOL') . ($product->price ?? 0) . '</td>
                        <td>
                            <input type="number" name="avail_quantity[' . $product->id . ']" class="form-control avail_quantity" step="1" min="1" stock-data="' . ($product->total_stock ?? 0) . '" placeholder="eg: 5" disabled>
                        </td>
                        <td>' . ($product->total_stock ?? 0) . '</td>
                        <td>' . ucwords(optional($product->category)->name ?? 'N/A') . '</td>
                    </tr>';
            }

            return response()->json([
                'html' => $html,
                'hasMore' => $products->hasMorePages(),
                'currentPage' => $products->currentPage(),
                'total' => $products->total()
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'error' => 'Error loading products: ' . $e->getMessage(),
                'html' => '',
                'hasMore' => false
            ], 500);
        }
    }
    public function getSubCategories($id)
    {
        $categories = Category::where('parent_category_id', $id)->get();
        if ($categories->isEmpty()) {
            return response('<option value="">No options available</option>');
        }
        $options = $categories->map(function ($category) {
            return '<option value="' . $category->id . '">' . $category->name . '</option>';
        })->join('');
        return response($options);
    }

    public function uploadDropzoneFiles(Request $request)
    {
        try {
            $image = $request->file('file');
            $image = $this->storeImage('products', $image);
            extract($request->all());
           $productImage = ProductImage::create([
                'timestamp' => $timestamp,
                'image' => $image,
                'is_primary' => $is_primary??0,
            ]);
            return response()->json(['success' => 'File uploaded successfully.', 'imageid' => $productImage->id ]);
        }catch (Exception $e){
            return response()->json(['error' => 'File not uploaded.']);
        }
    }

    public function updatePrimaryStatus(Request $request)
    {

        $imageId = $request->input('image_id');
        $isPrimary = $request->input('is_primary');

        $image = ProductImage::find($imageId);
        if ($image) {
            $image->is_primary = $isPrimary;
            $image->save();

            return response()->json(['success' => true]);
        }

        return response()->json(['success' => false, 'message' => 'Image not found']);
    }


    public function deleteImages(Request $request, $id)
    {
        $request->validate(['image_id' => 'required|exists:product_images,id']);

        $image = ProductImage::findOrFail($request->image_id);

        Storage::disk('public')->delete($image->image);

        $image->delete();

        return response()->json(['success' => true]);
    }

    /**
     * Helper method to truncate text with ellipsis
     */
    private function truncateText($text, $length = 20)
    {
        return strlen($text) > $length ? substr($text, 0, $length) . '...' : $text;
    }

}
