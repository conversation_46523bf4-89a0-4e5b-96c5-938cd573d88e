@extends('theme.layout.master')

@push('css')
    <style>
        .discount-step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6f42c1;
            margin-bottom: 20px;
        }
        .discount-step h5 {
            color: #495057;
            font-weight: 600;
        }
        .product-info-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .product_name_wrapper {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .product_img img {
            width: 60px;
            height: 60px;
            object-fit: cover;
            border-radius: 6px;
        }
        .product_name_details h5 {
            margin: 0 0 5px 0;
            font-weight: 600;
            color: #495057;
        }
        .product_name_details p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
    </style>
@endpush

@section('navbar-title')
@endsection

@section('content')
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2><i class="fa-solid fa-pen-to-square text-warning"></i> Edit Discount</h2>
                            <div class="custom_flex">
                                <a href="{{ route('products-discount.index') }}" class="btn_light">
                                    <i class="fa-solid fa-arrow-left"></i> Back to Discounts
                                </a>
                                <a href="{{ route('products-discount.show', $discount->id) }}" class="btn_purple">
                                    <i class="fa-solid fa-eye"></i> View Discount
                                </a>
                            </div>
                        </div>

                        <!-- Product Information (Read-only) -->
                        <div class="product-info-card">
                            <h5><i class="fa-solid fa-box text-primary"></i> Product Information</h5>
                            <div class="product_name_wrapper">
                                <div class="product_img">
                                    <img src="{{ $discount->product->image ? asset('website/' . $discount->product->image->image) : asset('website/assets/images/no-image.png') }}" alt="Product Image">
                                </div>
                                <div class="product_name_details">
                                    <h5>{{ $discount->product->name }}</h5>
                                    <p><strong>SKU:</strong> {{ $discount->product->sku_id ?? 'N/A' }}</p>
                                    <p><strong>Category:</strong> {{ $discount->product->category->name ?? 'N/A' }}</p>
                                    <p><strong>Price:</strong> £{{ number_format($discount->product->price, 2) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Edit Discount Form -->
                        <div class="discount-step">
                            <h5><i class="fa-solid fa-tag text-warning"></i> Edit Discount Details</h5>

                            <form action="{{ route('products-discount.update', $discount->id) }}" method="POST">
                                @csrf
                                @method('PUT')

                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="discount_percentage" class="form-label">Discount Percentage (%)</label>
                                            <input type="number"
                                                   class="form-control @error('discount_percentage') is-invalid @enderror"
                                                   id="discount_percentage"
                                                   name="discount_percentage"
                                                   value="{{ old('discount_percentage', $discount->discount_percentage) }}"
                                                   min="1"
                                                   max="100"
                                                   required>
                                            @error('discount_percentage')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="start_date" class="form-label">Start Date</label>
                                            <input type="date"
                                                   class="form-control @error('start_date') is-invalid @enderror"
                                                   id="start_date"
                                                   name="start_date"
                                                   value="{{ old('start_date', date('Y-m-d', strtotime($discount->start_date))) }}"
                                                   required>
                                            @error('start_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>

                                    <div class="col-md-4">
                                        <div class="form-group mb-3">
                                            <label for="end_date" class="form-label">End Date</label>
                                            <input type="date"
                                                   class="form-control @error('end_date') is-invalid @enderror"
                                                   id="end_date"
                                                   name="end_date"
                                                   value="{{ old('end_date', date('Y-m-d', strtotime($discount->end_date))) }}"
                                                   required>
                                            @error('end_date')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="discount_quantity" class="form-label">Discount Quantity (Optional)</label>
                                            <input type="number"
                                                   class="form-control @error('discount_quantity') is-invalid @enderror"
                                                   id="discount_quantity"
                                                   name="discount_quantity"
                                                   value="{{ old('discount_quantity', $discount->discount_quantity) }}"
                                                   min="1">
                                            @error('discount_quantity')
                                                <div class="invalid-feedback">{{ $message }}</div>
                                            @enderror
                                            <small class="form-text text-muted">Leave empty for unlimited discount quantity</small>
                                        </div>
                                    </div>
                                </div>

                                <div class="d-flex gap-2 mt-4">
                                    <button type="submit" class="btn_purple">
                                        <i class="fa-solid fa-save"></i> Update Discount
                                    </button>
                                    <a href="{{ route('products-discount.show', $discount->id) }}" class="btn_light">
                                        <i class="fa-solid fa-times"></i> Cancel
                                    </a>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection