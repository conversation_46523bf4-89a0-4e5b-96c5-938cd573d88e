@extends('theme.layout.master')

@push('css')
    <style>
        .tooltip-inner{background-color:black!important;color:white!important;}.bs-tooltip-top .tooltip-arrow::before{border-top-color:black!important;}.bs-tooltip-bottom .tooltip-arrow::before{border-bottom-color:black!important;}.bs-tooltip-start .tooltip-arrow::before{border-left-color:black!important;}.bs-tooltip-end .tooltip-arrow::before{border-right-color:black!important;}table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control:before,table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control:before{background-color:#6f42c1;border:2px solid #6f42c1;border-radius:50%;box-shadow:0 0 3px #ccc;color:white;content:'+';display:inline-block;font-family:'Courier New',Courier,monospace;font-size:14px;font-weight:bold;height:18px;line-height:14px;margin-right:5px;text-align:center;width:18px;}table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td.dtr-control:before,table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th.dtr-control:before{background-color:#dc3545;border-color:#dc3545;content:'-';}table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control,table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control{position:relative;cursor:pointer;text-align:center;}table.dataTable tbody td.dtr-control{text-align:center;cursor:pointer;}table.dataTable tbody tr.child{background-color:#f8f9fa;}table.dataTable tbody tr.child ul.dtr-details{margin:0;padding:10px;}table.dataTable tbody tr.child ul.dtr-details li{border-bottom:1px solid #dee2e6;padding:8px 0;list-style:none;}table.dataTable tbody tr.child ul.dtr-details li:last-child{border-bottom:none;}table.dataTable tbody tr.child ul.dtr-details li span.dtr-title{font-weight:bold;color:#495057;display:inline-block;width:120px;}.table_view_ban_icon_wrapper .btn-xs{padding:4px 8px!important;font-size:12px!important;margin:0 2px;border-radius:4px;}.table_view_ban_icon_wrapper{display:flex;gap:4px;justify-content:center;align-items:center;}
    </style>
@endpush
@section('navbar-title')

@endsection
@section('content')
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/total_product_listed.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Total Products Listed</h6>
                            <h2>{{$totalProductsCount??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic1.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Active Products</h6>
                            <h2>{{$activeProductsCount??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic3.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Products with Discounts</h6>
                            <h2>{{$productsWithDiscounts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/soldout.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Out of Stock</h6>
                            <h2>{{$outOfStockCount??0}}</h2>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2>Products with Discounts</h2>
                            <div class="custom_flex">
                                <div class="search_input txt_field">
                                    <input type="text" class="custom_search_box form-control " placeholder="Search...">
                                </div>
                                <button type="button" class="btn_purple" data-bs-toggle="modal" data-bs-target="#discount_history">Discount History <i class="fa-solid fa-clock-rotate-left"></i></button>
                                <button type="button" class="btn_orange" data-bs-toggle="modal" data-bs-target="#add_discount">Add Discount <i class="fa-solid fa-tag"></i></button>
                                @can('products-create')
                                    <a href="{{ route('products.create') }}" class="btn_maroon">Create New Product <i class="fa-solid fa-plus"></i></a>
                                @endcan
                                <div class="dropdown">
                                    <button data-bs-toggle="dropdown" aria-expanded="false" class="btn_purple dropdown-toggle"><img src="{{ asset('website') }}/assets/images/filter_icon_svg.svg"></button>
                                    <ul class="dropdown-menu" onclick="event.stopPropagation()">
                                        <li>
                                            <div class="form-group filter_checkbox_wrapper">
                                                <input type="checkbox" id="active_filter" class="filter_checkbox" value="active">
                                                <label for="active_filter">Active</label>
                                            </div>
                                        </li>
                                        <li>

                                            <div class="form-group filter_checkbox_wrapper">
                                                <input type="checkbox" id="inactive_filter" class="filter_checkbox" value="inactive">
                                                <label for="inactive_filter">Inactive</label>
                                            </div>
                                        </li>
                                    </ul>
                                </div>
                            </div>
                        </div>
                        <div class="table_wrapper">
                            <table class="table" id="products-table">
                                <thead>
                                <tr>
                                    <th>SKU ID</th>
                                    <th>Name</th>
                                    <th>Price</th>
                                    <th>Discount</th>
                                    <th>Stock</th>
                                    <th>Status</th>
                                    <th>Featured</th>
                                    <th>Category</th>
                                    <th>Seller</th>
                                    <th>Date Added</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <div class="modal dashboard_index_pg_sec add_modal product_add_discount_modal" id="add_discount" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1>Add Discount</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form action="{{route('products-discount.store')}}" method="POST" >
                        @csrf
                        <div class="row custom_row">
                            <div class="col-md-4">
                                <div class="form-group txt_field">
                                    <label for="">Discount Percentage</label>
                                    <input  type="text" class="form-control product_discount_percentage" placeholder="Enter Discount Percentage"   required   name="discount_percentage" >
                                    <i class="fa-solid fa-percent percent_input_icon"></i>
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="">Start Date</label>
                                    <input type="date" class="form-control" name="start_date" required min="{{date('Y-m-d')}}">
                                </div>
                            </div>
                            <div class="col-md-4">
                                <div class="form-group">
                                    <label for="">End Date</label>
                                    <input type="date" class="form-control" name="end_date" required min="{{date('Y-m-d')}}">
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_table">
                                    <div class="search_input txt_field mb-3">
                                        <input type="text" class="form-control" id="modal_product_search" placeholder="Search products...">
                                    </div>
                                    <table class="table" id="discount-products-table">
                                        <thead>
                                        <tr>
                                            <th><input type="checkbox" class="form-check-input" id="select_all_products"></th>
                                            <th for="select_all_products">Product</th>
                                            <th>Base Price</th>
                                            <th>Avail Discount</th>
                                            <th>Stock</th>
                                            <th>Category</th>
                                        </tr>
                                        </thead>
                                        <tbody id="discount-products-tbody">
                                            <!-- Products loaded via AJAX -->
                                        </tbody>
                                    </table>
                                    <div class="text-center mt-3">
                                        <button type="button" class="btn btn-secondary" id="load_more_products" style="display: none;">Load More Products</button>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class=" modal_buttons">
                                    <button type="button" class="btn_light" data-bs-dismiss="modal">Cancel <i class="fa-solid fa-x"></i></button>
                                    <button type="submit" class="btn_maroon">Add Discount <i class="fa-solid fa-check"></i></button>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
    <div class="modal dashboard_index_pg_sec add_modal product_add_discount_modal" id="discount_history" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h1>Discount History</h1>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="row custom_row">
                        <div class="col-md-12">
                            <div class="box_shadow_wrapper">
                                <div class="reviews_chart_wrapper">
                                    <h2>Products</h2>
                                    <div class="custom_flex">
                                        <div class="search_input txt_field">
                                            <input type="text" class="custom_search_box form-control " placeholder="Search...">
                                        </div>
                                    </div>
                                </div>
                                <div class="table_wrapper">
                                    <table  class="table myTable datatable products">
                                        <thead>
                                        <tr>
                                            <th>Product Name</th>
                                            <th>Discount %</th>
                                            <th>Start Date</th>
                                            <th>End Date</th>
                                            <th>Avail Quantity</th>
                                            <th>Remaining Quantity</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($productDiscounts as $discount)
                                            <tr>
                                                <td>{{ substr($discount->product->name, 0, 30) }}
                                                    {{ strlen($discount->product->name) > 30 ? '...' : '' }}</td>
                                                <td>{{ $discount->discount_percentage ?? 'N/A' }}</td>
                                                <td>{{ $discount->start_date??'N/A' }}</td>
                                                <td>{{ $discount->end_date??'N/A' }}</td>
                                                <td>{{ $discount->avail_quantity??'N/A' }}</td>
                                                <td>{{ $discount->remaining_quantity??'N/A' }}</td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                        </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script>
        $(document).ready(function () {
            // Show error next to input
            function showError(input, message) {
                removeError(input);
                input.after(`<small class="text-danger validation-error">${message}</small>`);
            }

            // Remove existing error
            function removeError(input) {
                input.closest('div').find('.validation-error').remove();
            }

            // Discount percentage validation and input restriction
            $('.product_discount_percentage').on('input', function () {
                let input = $(this);
                let value = input.val();

                // Allow only numbers and one decimal point
                value = value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');
                input.val(value);

                const num = parseFloat(value);
                removeError(input);

                if (isNaN(num) || num < 0.01 || num > 100) {
                    showError(input, 'Enter a value between 0.01 and 100');
                }
            });

            // Enable avail_quantity only if product is selected
            $('.select_product').on('change', function () {
                let row = $(this).closest('tr');
                let quantityInput = row.find('.avail_quantity');

                if ($(this).is(':checked')) {
                    quantityInput.prop('disabled', false).prop('required', true).val('');
                } else {
                    quantityInput.prop('disabled', true).prop('required', false).val('');
                    removeError(quantityInput);
                }
            });

            // Prevent avail_quantity > stock-data
            $(document).on('input', '.avail_quantity', function () {
                const input = $(this);
                const stock = parseInt(input.attr('stock-data'), 10);
                const value = parseInt(input.val(), 10);

                removeError(input);
                if (value > stock) {
                    showError(input, `Max stock is ${stock}`);
                    input.val('');
                }
            });
            $(document).on('input', '.avail_quantity', function () {
                let value = parseInt($(this).val());
                if (value < 1 || isNaN(value)) {
                    $(this).val('');
                }
            });

            // Date validation
            $('input[name="start_date"], input[name="end_date"]').on('change', function () {
                const start = new Date($('input[name="start_date"]').val());
                const end = new Date($('input[name="end_date"]').val());
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                let startInput = $('input[name="start_date"]');
                let endInput = $('input[name="end_date"]');

                removeError(startInput);
                removeError(endInput);

                if (start < today) {
                    showError(startInput, 'Start date cannot be in the past');
                    startInput.val('');
                }

                if (end < today) {
                    showError(endInput, 'End date cannot be in the past');
                    endInput.val('');
                }

                if (start && end && end < start) {
                    showError(endInput, 'End date must be after start date');
                    endInput.val('');
                }
            });

            // Form submission
            $('form').on('submit', function (e) {
                let isValid = true;
                $('.validation-error').remove();

                if ($('.select_product:checked').length === 0) {
                    isValid = false;
                    Swal.fire({
                        icon: 'error',
                        title: 'Oops...',
                        text: 'Please select at least one product.',
                    });
                }

                $('.select_product:checked').each(function () {
                    let row = $(this).closest('tr');
                    let input = row.find('.avail_quantity');
                    let stock = parseInt(input.attr('stock-data'), 10);
                    let value = parseInt(input.val(), 10);

                    if (!value || value <= 0) {
                        showError(input, 'Quantity required');
                        isValid = false;
                    } else if (value > stock) {
                        showError(input, `Max stock is ${stock}`);
                        isValid = false;
                    }
                });

                const discountInput = $('.product_discount_percentage');
                const discount = parseFloat(discountInput.val());

                if (isNaN(discount) || discount < 0.01 || discount > 100) {
                    showError(discountInput, 'Enter a valid discount (0.01 - 100)');
                    isValid = false;
                }

                const start = new Date($('input[name="start_date"]').val());
                const end = new Date($('input[name="end_date"]').val());
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (!start || start < today) {
                    showError($('input[name="start_date"]'), 'Invalid start date');
                    isValid = false;
                }

                if (!end || end < today || end < start) {
                    showError($('input[name="end_date"]'), 'Invalid end date');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Select All logic
            $('#select_all_products').on('change', function () {
                $('.select_product').prop('checked', this.checked).trigger('change');
            });

            $('.select_product').on('change', function () {
                const total = $('.select_product').length;
                const checked = $('.select_product:checked').length;
                $('#select_all_products').prop('checked', total === checked);
            });
        });
        $(document).ready(function(){
            $('.tooltip-trigger').tooltip();
        });

        // Products DataTable - Original working implementation
        let productsTable;

        $(document).ready(function() {
            // Ensure table exists before initializing
            if ($('#products-table').length) {
                productsTable = $('#products-table').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false,
                    ajax: {
                        url: '{{ route("products-discount.table-data") }}',
                        type: 'GET',
                        data: function (d) {
                            d.search.value = $('.custom_search_box').val();
                        },
                        error: function(xhr, error, thrown) {
                            console.log('DataTable AJAX Error:', xhr.responseText);
                            alert('Error loading products data. Please refresh the page.');
                        }
                    },
                    columns: [
                        { data: 'sku_id', name: 'sku_id' },
                        { data: 'name', name: 'name' },
                        { data: 'price', name: 'price' },
                        { data: 'discount', name: 'discount', orderable: false },
                        { data: 'stock', name: 'stock' },
                        { data: 'status', name: 'status', orderable: false },
                        { data: 'is_featured', name: 'is_featured', orderable: false },
                        { data: 'category', name: 'category' },
                        { data: 'seller', name: 'seller' },
                        { data: 'date_added', name: 'date_added' },
                        { data: 'actions', name: 'actions', orderable: false, searchable: false },
                    ],
                    order: [[0, 'asc']],
                    pageLength: 10,
                    responsive: {
                        details: {
                            type: 'column',
                            target: 'tr'
                        }
                    },
                    columnDefs: [
                        {
                            className: 'dtr-control',
                            orderable: false,
                            targets: 0
                        },
                        {
                            responsivePriority: 1,
                            targets: [0, 1, -1] // SKU ID, Product Name, and Actions always visible
                        },
                        {
                            responsivePriority: 2,
                            targets: [2, 3, 4, 5] // Price, Discount, Stock, Status second priority
                        },
                        {
                            responsivePriority: 10000,
                            targets: [6, 7, 8, 9] // Featured, Category, Seller and Date Added - lowest priority (will be hidden and moved to expandable)
                        }
                    ]
                });

                $('.custom_search_box').on('keyup', function () {
                    if (productsTable) {
                        productsTable.draw();
                    }
                });
            }


        });



        // Discount Modal Products AJAX Loading
        let currentPage = 1;
        let hasMoreProducts = true;
        let isLoading = false;

        function loadDiscountModalProducts(search = '', reset = false) {
            if (isLoading) return;

            if (reset) {
                currentPage = 1;
                $('#discount-products-tbody').empty();
                hasMoreProducts = true;
            }

            isLoading = true;
            $('#load_more_products').text('Loading...').prop('disabled', true);

            $.ajax({
                url: '{{ route("products.discount-modal-products") }}',
                type: 'GET',
                data: {
                    search: search,
                    page: currentPage
                },
                success: function(response) {
                    if (response.html) {
                        $('#discount-products-tbody').append(response.html);
                        hasMoreProducts = response.hasMore;
                        currentPage++;

                        if (hasMoreProducts) {
                            $('#load_more_products').show().text('Load More Products').prop('disabled', false);
                        } else {
                            $('#load_more_products').hide();
                        }
                    } else {
                        console.log('No HTML returned from server');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr.responseText);
                    alert('Error loading products: ' + error);
                },
                complete: function() {
                    isLoading = false;
                }
            });
        }

        // Load products when modal opens
        $('#add_discount').on('shown.bs.modal', function() {
            if ($('#discount-products-tbody').is(':empty')) {
                loadDiscountModalProducts('', true);
            }
        });

        // Search functionality
        $('#modal_product_search').on('keyup', function() {
            const search = $(this).val();
            loadDiscountModalProducts(search, true);
        });

        // Load more button
        $('#load_more_products').on('click', function() {
            const search = $('#modal_product_search').val();
            loadDiscountModalProducts(search, false);
        });

    </script>




@endpush

