@extends('theme.layout.master')

@push('css')
    <style>
        .tooltip-inner{background-color:black!important;color:white!important;}.bs-tooltip-top .tooltip-arrow::before{border-top-color:black!important;}.bs-tooltip-bottom .tooltip-arrow::before{border-bottom-color:black!important;}.bs-tooltip-start .tooltip-arrow::before{border-left-color:black!important;}.bs-tooltip-end .tooltip-arrow::before{border-right-color:black!important;}table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control:before,table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control:before{background-color:#6f42c1;border:2px solid #6f42c1;border-radius:50%;box-shadow:0 0 3px #ccc;color:white;content:'+';display:inline-block;font-family:'Courier New',Courier,monospace;font-size:14px;font-weight:bold;height:18px;line-height:14px;margin-right:5px;text-align:center;width:18px;}table.dataTable.dtr-inline.collapsed>tbody>tr.parent>td.dtr-control:before,table.dataTable.dtr-inline.collapsed>tbody>tr.parent>th.dtr-control:before{background-color:#dc3545;border-color:#dc3545;content:'-';}table.dataTable.dtr-inline.collapsed>tbody>tr>td.dtr-control,table.dataTable.dtr-inline.collapsed>tbody>tr>th.dtr-control{position:relative;cursor:pointer;text-align:center;}table.dataTable tbody td.dtr-control{text-align:center;cursor:pointer;}table.dataTable tbody tr.child{background-color:#f8f9fa;}table.dataTable tbody tr.child ul.dtr-details{margin:0;padding:10px;}table.dataTable tbody tr.child ul.dtr-details li{border-bottom:1px solid #dee2e6;padding:8px 0;list-style:none;}table.dataTable tbody tr.child ul.dtr-details li:last-child{border-bottom:none;}table.dataTable tbody tr.child ul.dtr-details li span.dtr-title{font-weight:bold;color:#495057;display:inline-block;width:120px;}.table_view_ban_icon_wrapper .btn-xs{padding:4px 8px!important;font-size:12px!important;margin:0 2px;border-radius:4px;}.table_view_ban_icon_wrapper{display:flex;gap:4px;justify-content:center;align-items:center;}

        /* Improved Add Discount Modal Styles */
        .product_add_discount_modal .modal-dialog {
            max-width: 900px;
        }

        .discount-step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6f42c1;
        }

        .discount-step h5 {
            color: #495057;
            font-weight: 600;
        }

        .product-selection-step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }

        .product-selection-step h5 {
            color: #495057;
            font-weight: 600;
        }

        .table-responsive {
            border: 1px solid #dee2e6;
            border-radius: 6px;
        }

        .sticky-top {
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .product_name_wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .product_img img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }

        .product_name_details h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
        }

        .product_name_details p {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }

        #submit-discount:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }

        .alert-info {
            background-color: #e3f2fd;
            border-color: #2196f3;
            color: #1976d2;
        }
    </style>
@endpush
@section('navbar-title')

@endsection
@section('content')
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic3.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>All Discounted Products</h6>
                            <h2>{{$allDiscountedProducts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic1.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Active Discounted Products</h6>
                            <h2>{{$activeDiscountedProducts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/soldout.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Expired Discounts</h6>
                            <h2>{{$expiredDiscounts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/total_product_listed.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Upcoming Discounts</h6>
                            <h2>{{$upcomingDiscounts??0}}</h2>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2>Products with Discounts</h2>
                            <div class="custom_flex">
                                <div class="search_input txt_field">
                                    <input type="text" class="custom_search_box form-control " placeholder="Search products with discounts...">
                                </div>
                                <a href="{{ route('products-discount.create') }}" class="btn_orange">Add Discount <i class="fa-solid fa-tag"></i></a>
                            </div>
                        </div>
                        <div class="table_wrapper">
                            <table class="table" id="products-table">
                                <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Discount</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Stock</th>
                                    <th>Category</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



@endsection
@push('js')
    <script>
        $(document).ready(function () {
            // Show error next to input
            function showError(input, message) {
                removeError(input);
                input.after(`<small class="text-danger validation-error">${message}</small>`);
            }

            // Remove existing error
            function removeError(input) {
                input.closest('div').find('.validation-error').remove();
            }

            // Discount percentage validation and input restriction
            $('.product_discount_percentage').on('input', function () {
                let input = $(this);
                let value = input.val();

                // Allow only numbers and one decimal point
                value = value.replace(/[^0-9.]/g, '').replace(/(\..*?)\..*/g, '$1');
                input.val(value);

                const num = parseFloat(value);
                removeError(input);

                if (isNaN(num) || num < 0.01 || num > 100) {
                    showError(input, 'Enter a value between 0.01 and 100');
                }
            });

            // Enable avail_quantity only if product is selected
            $('.select_product').on('change', function () {
                let row = $(this).closest('tr');
                let quantityInput = row.find('.avail_quantity');

                if ($(this).is(':checked')) {
                    quantityInput.prop('disabled', false).prop('required', true).val('');
                } else {
                    quantityInput.prop('disabled', true).prop('required', false).val('');
                    removeError(quantityInput);
                }
            });

            // Prevent avail_quantity > stock-data
            $(document).on('input', '.avail_quantity', function () {
                const input = $(this);
                const stock = parseInt(input.attr('stock-data'), 10);
                const value = parseInt(input.val(), 10);

                removeError(input);
                if (value > stock) {
                    showError(input, `Max stock is ${stock}`);
                    input.val('');
                }
            });
            $(document).on('input', '.avail_quantity', function () {
                let value = parseInt($(this).val());
                if (value < 1 || isNaN(value)) {
                    $(this).val('');
                }
            });

            // Date validation
            $('input[name="start_date"], input[name="end_date"]').on('change', function () {
                const start = new Date($('input[name="start_date"]').val());
                const end = new Date($('input[name="end_date"]').val());
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                let startInput = $('input[name="start_date"]');
                let endInput = $('input[name="end_date"]');

                removeError(startInput);
                removeError(endInput);

                if (start < today) {
                    showError(startInput, 'Start date cannot be in the past');
                    startInput.val('');
                }

                if (end < today) {
                    showError(endInput, 'End date cannot be in the past');
                    endInput.val('');
                }

                if (start && end && end < start) {
                    showError(endInput, 'End date must be after start date');
                    endInput.val('');
                }
            });

            // Form submission with improved validation
            $('form').on('submit', function (e) {
                let isValid = true;
                $('.validation-error').remove();

                // Check if products are selected
                if ($('.select_product:checked').length === 0) {
                    isValid = false;
                    Swal.fire({
                        icon: 'warning',
                        title: 'No Products Selected',
                        text: 'Please select at least one product to apply the discount.',
                        confirmButtonColor: '#6f42c1'
                    });
                    return false;
                }

                $('.select_product:checked').each(function () {
                    let row = $(this).closest('tr');
                    let input = row.find('.avail_quantity');
                    let stock = parseInt(input.attr('stock-data'), 10);
                    let value = parseInt(input.val(), 10);

                    if (!value || value <= 0) {
                        showError(input, 'Quantity required');
                        isValid = false;
                    } else if (value > stock) {
                        showError(input, `Max stock is ${stock}`);
                        isValid = false;
                    }
                });

                const discountInput = $('.product_discount_percentage');
                const discount = parseFloat(discountInput.val());

                if (isNaN(discount) || discount < 0.01 || discount > 100) {
                    showError(discountInput, 'Enter a valid discount (0.01 - 100)');
                    isValid = false;
                }

                const start = new Date($('input[name="start_date"]').val());
                const end = new Date($('input[name="end_date"]').val());
                const today = new Date();
                today.setHours(0, 0, 0, 0);

                if (!start || start < today) {
                    showError($('input[name="start_date"]'), 'Invalid start date');
                    isValid = false;
                }

                if (!end || end < today || end < start) {
                    showError($('input[name="end_date"]'), 'Invalid end date');
                    isValid = false;
                }

                if (!isValid) {
                    e.preventDefault();
                }
            });

            // Select All logic with improved feedback
            $('#select_all_products').on('change', function () {
                $('.select_product').prop('checked', this.checked).trigger('change');
                updateSelectionInfo();
            });

            $(document).on('change', '.select_product', function () {
                const total = $('.select_product').length;
                const checked = $('.select_product:checked').length;
                $('#select_all_products').prop('checked', total === checked);
                updateSelectionInfo();
            });

            // Update selection info
            function updateSelectionInfo() {
                const selectedCount = $('.select_product:checked').length;
                $('#selected-count').text(selectedCount);

                if (selectedCount > 0) {
                    $('#selection-info').show();
                    $('#submit-discount').prop('disabled', false);
                } else {
                    $('#selection-info').hide();
                    $('#submit-discount').prop('disabled', true);
                }
            }
        });
        $(document).ready(function(){
            $('.tooltip-trigger').tooltip();
        });

        // Products DataTable - Original working implementation
        let productsTable;

        $(document).ready(function() {
            // Ensure table exists before initializing
            if ($('#products-table').length) {
                productsTable = $('#products-table').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false,
                    ajax: {
                        url: '{{ route("products-discount.table-data") }}',
                        type: 'GET',
                        data: function (d) {
                            d.search.value = $('.custom_search_box').val();
                            d._t = new Date().getTime(); // Cache busting
                        },
                        error: function(xhr, error, thrown) {
                            console.log('DataTable AJAX Error:', xhr.responseText);
                            alert('Error loading products data. Please refresh the page.');
                        }
                    },
                    columns: [
                        { data: 'product', name: 'product', orderable: false },
                        { data: 'price', name: 'price' },
                        { data: 'discount', name: 'discount', orderable: false },
                        { data: 'start_date', name: 'start_date' },
                        { data: 'end_date', name: 'end_date' },
                        { data: 'status', name: 'status', orderable: false },
                        { data: 'stock', name: 'stock' },
                        { data: 'category', name: 'category' },
                        { data: 'actions', name: 'actions', orderable: false, searchable: false },
                    ],
                    order: [[0, 'asc']],
                    pageLength: 10,
                    responsive: {
                        details: {
                            type: 'column',
                            target: 'tr'
                        }
                    },
                    columnDefs: [
                        {
                            className: 'dtr-control',
                            orderable: false,
                            targets: 0
                        },
                        {
                            responsivePriority: 1,
                            targets: [0, 1, -1] // SKU ID, Product Name, and Actions always visible
                        },
                        {
                            responsivePriority: 2,
                            targets: [2, 3, 4] // Price, Discount, Stock second priority
                        },
                        {
                            responsivePriority: 3,
                            targets: [5] // Category third priority
                        }
                    ]
                });

                $('.custom_search_box').on('keyup', function () {
                    if (productsTable) {
                        productsTable.draw();
                    }
                });
            }


        });



        // Discount Modal Products AJAX Loading
        let currentPage = 1;
        let hasMoreProducts = true;
        let isLoading = false;

        function loadDiscountModalProducts(search = '', reset = false) {
            if (isLoading) return;

            if (reset) {
                currentPage = 1;
                $('#discount-products-tbody').empty();
                hasMoreProducts = true;
            }

            isLoading = true;
            $('#load_more_products').text('Loading...').prop('disabled', true);

            $.ajax({
                url: '{{ route("products.discount-modal-products") }}',
                type: 'GET',
                data: {
                    search: search,
                    page: currentPage
                },
                success: function(response) {
                    if (response.html) {
                        $('#discount-products-tbody').append(response.html);
                        hasMoreProducts = response.hasMore;
                        currentPage++;

                        if (hasMoreProducts) {
                            $('#load_more_products').show().text('Load More Products').prop('disabled', false);
                        } else {
                            $('#load_more_products').hide();
                        }
                    } else {
                        console.log('No HTML returned from server');
                    }
                },
                error: function(xhr, status, error) {
                    console.log('AJAX Error:', xhr.responseText);
                    alert('Error loading products: ' + error);
                },
                complete: function() {
                    isLoading = false;
                }
            });
        }

        // Load products when modal opens
        $('#add_discount').on('shown.bs.modal', function() {
            if ($('#discount-products-tbody').is(':empty')) {
                loadDiscountModalProducts('', true);
            }
        });

        // Search functionality
        $('#modal_product_search').on('keyup', function() {
            const search = $(this).val();
            loadDiscountModalProducts(search, true);
        });

        // Load more button
        $('#load_more_products').on('click', function() {
            const search = $('#modal_product_search').val();
            loadDiscountModalProducts(search, false);
        });

    </script>




@endpush

