@extends('theme.layout.master')

@push('css')
    <style>
        /* DataTable Action Buttons */
        .table_view_ban_icon_wrapper .btn-xs {
            padding: 4px 8px !important;
            font-size: 12px !important;
            margin: 0 2px;
            border-radius: 4px;
        }

        .table_view_ban_icon_wrapper {
            display: flex;
            gap: 4px;
            justify-content: center;
            align-items: center;
        }

        /* Product Display in Table */
        .product_name_wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .product_img img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }

        .product_name_details h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
        }

        .product_name_details p {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }
    </style>
@endpush
@section('navbar-title')

@endsection
@section('content')
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic3.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>All Discounted Products</h6>
                            <h2>{{$allDiscountedProducts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/ic1.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Active Discounted Products</h6>
                            <h2>{{$activeDiscountedProducts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/soldout.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Expired Discounts</h6>
                            <h2>{{$expiredDiscounts??0}}</h2>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="box_shadow_wrapper cards_wrapper">
                        <div class="total_user_wrapper_img">
                            <div class="total_user_img">
                                <img src="{{ asset('website') }}/assets/images/total_product_listed.svg">
                            </div>
                        </div>
                        <div class="total_user_wrapper_details">
                            <h6>Upcoming Discounts</h6>
                            <h2>{{$upcomingDiscounts??0}}</h2>
                        </div>
                    </div>
                </div>

                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2><i class="fa-solid fa-tags text-warning"></i> Products with Discounts</h2>
                            <div class="custom_flex">
                                <div class="search_input txt_field">
                                    <input type="text" class="custom_search_box form-control " placeholder="Search products with discounts...">
                                </div>
                                <a href="{{ route('products-discount.create') }}" class="btn_orange">Add Discount <i class="fa-solid fa-tag"></i></a>
                            </div>
                        </div>
                        <div class="table_wrapper">
                            <table class="table" id="products-table">
                                <thead>
                                <tr>
                                    <th>Product</th>
                                    <th>Price</th>
                                    <th>Discount</th>
                                    <th>Start Date</th>
                                    <th>End Date</th>
                                    <th>Status</th>
                                    <th>Stock</th>
                                    <th>Category</th>
                                    <th>Actions</th>
                                </tr>
                                </thead>
                                <tbody>
                                <!-- Data will be loaded via AJAX -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>



@endsection
@push('js')
    <script>
        // Products DataTable
        let productsTable;

        $(document).ready(function() {
            // Initialize DataTable
            if ($('#products-table').length) {
                productsTable = $('#products-table').DataTable({
                    processing: true,
                    serverSide: true,
                    searching: false,
                    ajax: {
                        url: '{{ route("products-discount.table-data") }}',
                        type: 'GET',
                        data: function (d) {
                            d.search.value = $('.custom_search_box').val();
                            d._t = new Date().getTime(); // Cache busting
                        },
                        dataSrc: function(json) {
                            console.log('DataTable Response:', json);
                            if (json.data && json.data.length > 0) {
                                console.log('First row data:', json.data[0]);
                            }
                            return json.data;
                        },
                        error: function(xhr, error, thrown) {
                            console.log('DataTable AJAX Error:', xhr.responseText);
                            alert('Error loading products data. Please refresh the page.');
                        }
                    },
                    columns: [
                        { data: 'product', name: 'product', orderable: false, searchable: false },
                        { data: 'price', name: 'price' },
                        { data: 'discount', name: 'discount', orderable: false, searchable: false },
                        { data: 'start_date', name: 'start_date' },
                        { data: 'end_date', name: 'end_date' },
                        { data: 'status', name: 'status', orderable: false, searchable: false },
                        { data: 'stock', name: 'stock' },
                        { data: 'category', name: 'category' },
                        { data: 'actions', name: 'actions', orderable: false, searchable: false }
                    ],
                    order: [[1, 'asc']], // Order by price instead of product
                    pageLength: 10,
                    responsive: true
                });

                // Search functionality
                $('.custom_search_box').on('keyup', function () {
                    if (productsTable) {
                        productsTable.draw();
                    }
                });
            }
        });

    </script>
@endpush

