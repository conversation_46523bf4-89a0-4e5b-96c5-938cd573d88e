@extends('theme.layout.master')

@push('css')
    <style>
        .discount-details-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border-left: 4px solid #6f42c1;
            margin-bottom: 20px;
        }
        .product-info-card {
            background: #fff;
            padding: 20px;
            border-radius: 8px;
            border: 1px solid #dee2e6;
            margin-bottom: 20px;
        }
        .product_name_wrapper {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        .product_img img {
            width: 80px;
            height: 80px;
            object-fit: cover;
            border-radius: 8px;
        }
        .product_name_details h4 {
            margin: 0 0 5px 0;
            font-weight: 600;
            color: #495057;
        }
        .product_name_details p {
            margin: 0;
            color: #6c757d;
            font-size: 14px;
        }
        .discount-info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        .info-item {
            background: #fff;
            padding: 15px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }
        .info-item label {
            font-weight: 600;
            color: #495057;
            font-size: 12px;
            text-transform: uppercase;
            margin-bottom: 5px;
            display: block;
        }
        .info-item .value {
            font-size: 16px;
            color: #212529;
            font-weight: 500;
        }
    </style>
@endpush

@section('navbar-title')
@endsection

@section('content')
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2><i class="fa-solid fa-eye text-info"></i> Discount Details</h2>
                            <div class="custom_flex">
                                <a href="{{ route('products-discount.index') }}" class="btn_light">
                                    <i class="fa-solid fa-arrow-left"></i> Back to Discounts
                                </a>
                                <a href="{{ route('products-discount.edit', $discount->id) }}" class="btn_purple">
                                    <i class="fa-solid fa-pen-to-square"></i> Edit Discount
                                </a>
                            </div>
                        </div>

                        <!-- Product Information -->
                        <div class="product-info-card">
                            <h5><i class="fa-solid fa-box text-primary"></i> Product Information</h5>
                            <div class="product_name_wrapper">
                                <div class="product_img">
                                    <img src="{{ $discount->product->image ? asset('website/' . $discount->product->image->image) : asset('website/assets/images/no-image.png') }}" alt="Product Image">
                                </div>
                                <div class="product_name_details">
                                    <h4>{{ $discount->product->name }}</h4>
                                    <p><strong>SKU:</strong> {{ $discount->product->sku_id ?? 'N/A' }}</p>
                                    <p><strong>Category:</strong> {{ $discount->product->category->name ?? 'N/A' }}</p>
                                    <p><strong>Price:</strong> £{{ number_format($discount->product->price, 2) }}</p>
                                </div>
                            </div>
                        </div>

                        <!-- Discount Details -->
                        <div class="discount-details-card">
                            <h5><i class="fa-solid fa-tag text-warning"></i> Discount Information</h5>

                            <div class="discount-info-grid">
                                <div class="info-item">
                                    <label>Discount Percentage</label>
                                    <div class="value">
                                        <span class="badge bg-success fs-6">{{ $discount->discount_percentage }}%</span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <label>Start Date</label>
                                    <div class="value">{{ date('d M Y', strtotime($discount->start_date)) }}</div>
                                </div>

                                <div class="info-item">
                                    <label>End Date</label>
                                    <div class="value">{{ date('d M Y', strtotime($discount->end_date)) }}</div>
                                </div>

                                <div class="info-item">
                                    <label>Status</label>
                                    <div class="value">
                                        @php
                                            $now = now();
                                            $startDate = \Carbon\Carbon::parse($discount->start_date);
                                            $endDate = \Carbon\Carbon::parse($discount->end_date);

                                            if ($now->lt($startDate)) {
                                                $status = 'Upcoming';
                                                $statusClass = 'bg-info';
                                            } elseif ($now->between($startDate, $endDate)) {
                                                $status = 'Active';
                                                $statusClass = 'bg-success';
                                            } else {
                                                $status = 'Expired';
                                                $statusClass = 'bg-danger';
                                            }
                                        @endphp
                                        <span class="badge {{ $statusClass }} fs-6">{{ $status }}</span>
                                    </div>
                                </div>

                                <div class="info-item">
                                    <label>Discount Quantity</label>
                                    <div class="value">{{ $discount->discount_quantity ?? 'N/A' }}</div>
                                </div>

                                <div class="info-item">
                                    <label>Created Date</label>
                                    <div class="value">{{ date('d M Y', strtotime($discount->created_at)) }}</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
@endsection