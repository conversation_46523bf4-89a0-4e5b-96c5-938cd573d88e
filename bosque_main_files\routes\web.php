<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\{
    ThemeController,
    WebsiteController,
    ChatBotsController,
    ProductsController,
    RoleController,
    UserController,
    CrudGeneratorController,
    SumsubController,
    CategoriesController,
    ProductImagesController,
    ProductVariantsController,
    SizesController,
    ProductDiscountsController,
    ProductVariantAttributesController,
    ProductAttributesController,
    CartsController,
    WishlistsController,
    OrdersController,
    OrderDetailsController,
    NotificationController,
    PdfController,
    UsersWalletHistoriesController
};

Route::get('clear-all', function() {
    Artisan::call('config:cache');
    Artisan::call('optimize:clear');
    Artisan::call('cache:clear');

    return "<h1 style='text-align: center'>Cache cleared successfully</h1><h1 style='text-align:center'><a href='".url('/')."'>Go to Home</a><br><a href='".url('clear-all')."'>Clear Again</a><br> ------------------------</h1>";
});

Route::get('testing',[WebsiteController::class, 'testing'])->name('testing');
//Route::get('/', [WebsiteController::class, 'index'])->name('index');
Route::get('/', [WebsiteController::class, 'index'])
    ->name('index')
    ->withoutMiddleware([SetTimezone::class]);

Route::get('/logout', function () {
    Auth::logout(); return redirect('/');
});
Auth::routes();

Route::post('update-user-sumsub-status', [UserController::class,'updateUserSumsubStatus'])->name('update-user-sumsub-status')->middleware('auth');
Route::get('update-user-sumsub-status', function(){
    return redirect(url('dashboard'))->with(['title'=>'Error','message'=>'Invalid request','type'=>'error']);
});
Route::post('/calculate-shipping', [WebsiteController::class, 'calculateShipping'])->name('calculate-shipping');
Route::resource('users', UserController::class);
Route::group(['middleware' => ['auth','sumsub.verification']], function () {
    Route::get('crud_generator', [CrudGeneratorController::class, 'crudGenerator'])->name('crud_generator');
    Route::post('crud_generator_process', [CrudGeneratorController::class, 'crudGeneratorProcess'])->name('crud_generator_process');
    Route::resource("settings", "\App\Http\Controllers\SettingsController");
    Route::get('permissions', [ThemeController::class, 'permissions'])->name('permissions');
    Route::get('dashboard', [WebsiteController::class, 'dashboard'])->name('dashboard');
    Route::prefix('products')->group(function () {
        Route::get('table-data', [ProductsController::class, 'getTableData'])->name('products.table-data');
        Route::get('discount-modal-products', [ProductsController::class, 'getDiscountModalProducts'])->name('products.discount-modal-products');
        Route::get('dashboard-stats', [ProductsController::class, 'getDashboardStats'])->name('products.dashboard-stats');
    });
    Route::resource("products", ProductsController::class);
    Route::prefix('products-discount')->group(function () {
        Route::get('table-data', [ProductDiscountsController::class, 'getTableData'])->name('products-discount.table-data');
        Route::get('get-products', [ProductDiscountsController::class, 'getProducts'])->name('products-discount.get-products');
    });
    Route::resource("products-discount", ProductDiscountsController::class);
    Route::prefix('categories')->group(function () {
        Route::get('table-data', [CategoriesController::class, 'getTableData'])->name('categories.table-data');
    });
    Route::resource("categories", CategoriesController::class);
    Route::resource("productimages", ProductImagesController::class);
    Route::resource("productvariants",  ProductVariantsController::class);
    Route::resource("sizes", SizesController::class);
    // Route::resource("productvariantattributes", ProductVariantAttributesController::class); // Controller missing
    Route::resource("productattributes", ProductAttributesController::class);
    Route::resource("carts", CartsController::class);
    Route::resource("wishlists",    WishlistsController::class);
    Route::resource("orderdetails", OrderDetailsController::class);
    Route::get('/home', function () {
        if (auth()->user()) {
            return redirect('dashboard');
        } else {
            return redirect('login');
            return view('auth.login');
        }
    })->middleware('auth');
    Route::resource('roles', RoleController::class);
    Route::get('funds', [WebsiteController::class, 'funds'])->name('funds');
    Route::get('inventory-reports', [WebsiteController::class, 'inventoryReports'])->name('inventory-reports');
    Route::get('inventory-details', [WebsiteController::class, 'inventoryDetails'])->name('inventory-details');
    Route::get('sales-reports', [WebsiteController::class, 'salesReports'])->name('sales-reports');
    Route::get('order-reports', [WebsiteController::class, 'orderReports'])->name('order-reports');
    Route::resource("orders", OrdersController::class);
    Route::post('update-order-status', [OrdersController::class, 'updateOrderStatus'])->name('update-order-status');
    Route::get('withdrawal-request', [WebsiteController::class, 'withdrawalRequest'])->name('withdrawal-request');
    Route::get('withdraw-request-ajax/{id?}', [WebsiteController::class, 'withdrawRequestAjax'])->name('withdraw-request-ajax');
    Route::get('recent-order-inventory-details', [WebsiteController::class, 'recentOrderInventoryDetails'])->name('recent-order-inventory-details');
    Route::get('customer-reports', [WebsiteController::class, 'customerReports'])->name('customer-reports');
    Route::get('customer-inventory-details', [WebsiteController::class, 'customerInventoryDetails'])->name('customer-inventory-details');
    Route::get('order-inventory-details/{id?}', [WebsiteController::class, 'orderInventoryDetails'])->name('order-inventory-details');
    Route::get('sales-inventory-details', [WebsiteController::class, 'salesInventoryDetails'])->name('sales-inventory-details');
    Route::get('product-catalog', [WebsiteController::class, 'productCatalog'])->name('product-catalog');
    Route::get('payments', [WebsiteController::class, 'payments'])->name('payments');
    Route::get('notifications', [WebsiteController::class, 'notifications'])->name('notifications');
    Route::get('Payment-inventory-details/{id?}', [WebsiteController::class, 'paymentInventoryDetails'])->name('Payment-inventory-details');
    Route::get('create-new-product', [WebsiteController::class, 'createNewProduct'])->name('create-new-product');
    Route::get('product-catalog-details', [WebsiteController::class, 'productCatalogDetails'])->name('product-catalog-details');

});

Route::any('add-to-cart', [WebsiteController::class, 'addToCart']);
Route::post('add-to-wishlist', [WebsiteController::class, 'addToWishlist']);
Route::post('cart-item-quantity',[WebsiteController::class,'cartItemQuantity']);
Route::get('show-cart-items',[WebsiteController::class,'showCartItems']);
Route::get('delete-cart-item/{id?}',[WebsiteController::class,'deleteCartItem'])->name('delete-cart-item');

Route::post('store-order',[WebsiteController::class,'storeOrder'])->name('store-order');
Route::get('success/{sessionId?}', [WebsiteController::class, 'success'])->name('success');
Route::get('cancel', [WebsiteController::class, 'cancel'])->name('cancel');

Route::get('terms-conditions', [WebsiteController::class, 'termsConditions'])->name('terms-conditions');
Route::get('customer-support', [WebsiteController::class, 'customerSupport'])->name('customer-support');
Route::get('delivery-details', [WebsiteController::class, 'deliveryDetails'])->name('delivery-details');
Route::get('privacy-policy', [WebsiteController::class, 'privacyPolicy'])->name('privacy-policy');
Route::get('make-payment', [WebsiteController::class, 'makePayment'])->name('make-payment');
Route::get('delivery-options', [WebsiteController::class, 'deliveryOptions'])->name('delivery-options');
Route::get('buyer-protection', [WebsiteController::class, 'buyerProtection'])->name('buyer-protection');

Route::get('view-details', [WebsiteController::class, 'viewDetails'])->name('view-details');
Route::get('order_history', [WebsiteController::class, 'orderHistory'])->name('order_history');
Route::get('place-order', [WebsiteController::class, 'placeOrder'])->name('place-order');
Route::get('account_settings', [WebsiteController::class, 'accountSettings'])->name('account_settings')->middleware('auth');
Route::post('account_update/{id}', [WebsiteController::class, 'accountUpdate'])->name('account_update');
Route::post('update_password', [WebsiteController::class, 'updatePassword'])->name('update_password');
Route::get('product/{id}', [WebsiteController::class, 'product'])->name('product');
Route::get('product-detail-modal/{product_id?}', [WebsiteController::class, 'productDetailModal'])->name('product-detail-modal');
Route::get('fetch-product-by-category/{id}/{chat_id?}', [WebsiteController::class, 'fetchProductByCategory'])->name('fetch-product-by-category');

Route::get('wishlist', [WebsiteController::class, 'wishlist'])->name('wishlist');
Route::get('category/{unique?}', [WebsiteController::class, 'category'])->name('category');
Route::group(['middleware' => ['auth', 'role:seller']], function () {

});
Route::get('update-status/{model}/{id}/{status}', [WebsiteController::class, 'updateStatus'])->name('update-status');
Route::get('update-featured/{model}/{id}/{featured}', [WebsiteController::class, 'updateFeatured'])->name('update-featured');
Route::get('get-sub-categories/{id}', [ProductsController::class, 'getSubCategories'])->name('get-sub-categories');
Route::post('upload-dropzone-files', [ProductsController::class, 'uploadDropzoneFiles'])->name('upload-dropzone-files');
Route::post('update-primary-status', [ProductsController::class, 'updatePrimaryStatus']);
Route::post('/sumsub/access-token', [SumSubController::class, 'getAccessToken']);

Route::get('/notifications', [NotificationController::class, 'index'])->name('notifications.index');
Route::post('/notifications/{id}/mark-as-read', [NotificationController::class, 'markAsRead'])->name('notifications.markAsRead');
Route::post('/notifications/mark-all-as-read', [NotificationController::class, 'markAllAsRead'])->name('notifications.markAllAsRead');
Route::delete('/notifications/{id}', [NotificationController::class, 'destroy'])->name('notifications.destroy');
Route::get('/notifications/unread-count', [NotificationController::class, 'unreadCount'])->name('notifications.unreadCount');
Route::post('image-delete/{id}', [ProductsController::class, 'deleteImages'])->name('image-delete');
Route::get('/get-users-data/{year}', [WebsiteController::class, 'getUserDataForYear']);
Route::get('/get-orders-data/{year}', [WebsiteController::class, 'getOrderDataForYear']);
Route::get('/get-top-products/{year}', [WebsiteController::class, 'getTopProductsForYear']);
Route::get('/search-products', [WebsiteController::class, 'search'])->name('search.products');
Route::any('/search-product', [WebsiteController::class, 'searchProduct'])->name('search-product');
Route::get('/download-pdf/{id}', [PdfController::class, 'downloadPDF']);
Route::resource('users-wallet-histories', UsersWalletHistoriesController::class);
Route::post('send-amount', [UsersWalletHistoriesController::class, 'sendAmount'])->name('send-amount');
Route::get('buyer-notifications', [WebsiteController::class, 'buyerNotifications'])->name('buyer-notification');
Route::resource("userswallets", "\App\Http\Controllers\UsersWalletsController")->middleware("auth");
Route::resource("userswallethistories", "\App\Http\Controllers\UsersWalletHistoriesController")->middleware("auth");
Route::any('guest-login', [WebsiteController::class, 'guestLogin'])->name('guest-login');
Route::resource("chatbots", "\App\Http\Controllers\ChatBotsController")->middleware("auth");
Route::any("chat", [ChatBotsController::class,'sendMessage'])->name('chat.sendMessage');
Route::get('gsap_testing',[WebsiteController::class, 'gsapTesting'])->name('gsapTesting');
Route::post('/clear-all-cart', [WebsiteController::class, 'clearAllCartItems'])->name('clear.all.cart');
Route::get('/rate-product/{order_id?}/{rating?}', [OrdersController::class, 'rateProduct']);

Route::get('/email-exists', [WebsiteController::class, 'emailExists'])->name('email.exists');
Route::resource("content-managements", "\App\Http\Controllers\ContentManagementsController")->middleware("auth");
Route::get('/phone-exists', [WebsiteController::class, 'phoneExists'])->name('phone.exists');




