<?php $__env->startSection('content'); ?>
    <?php if($featuredCategories->count()>0): ?>
        <section class="category_slider_sec">
            <div class="container custom_container">
                <div class="row custom_row">
                    <div class="col-md-12">
                        <div class="">
                            <div class="swiper category_slider">
                                <div class="swiper-wrapper">
                                    <?php $__currentLoopData = $featuredCategories->where('is_featured', 1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $featuredCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide">
                                            <div class="category_slider_wrapper">
                                                <a href="<?php echo e(url('category',$featuredCategory->unique)); ?>">
                                                    <div class="category_slider_img">
                                                        <img src="<?php echo e(asset('website')); ?>/<?php echo e($featuredCategory->image); ?>">
                                                        <div class="img_text_overlay">
                                                            <p class="img_txt"><?php echo e($featuredCategory->name); ?></p>
                                                        </div>
                                                    </div>
                                                </a>
                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                                <div class="swiper-pagination"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <section >
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12"></div>
            </div>
        </div>
    </section>
    <?php if($discountedProducts->count()>0): ?>
        <section class="product_sale_sec" id="product_sale_sec" >
            <div class="container custom_container">
                <div class="row custom_row__gap">
                    <div class="col-md-12">
                        <div class="timer_heading_wrapper">
                            <h3>Super Deals</h3>
                            <div class="end_sale_time_wrapper">
                                <i class="fa-solid fa-clock"></i>
                                <label>Ends in:</label>
                                <div class="timer-wrapper">
                                    <span class="timer"></span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="super_deals_slider_wrapper">
                            <div class="swiper product-slider">
                                <div class="swiper-wrapper">
                                    <?php $__currentLoopData = $discountedProducts; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $discountedProduct): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <div class="swiper-slide">
                                            <div class="single_product_wrapper">
                                                <div>
                                                    <div class="product_img_saleoff_wrapper product_img_saleoff_wrapper_category">
                                                        <div class="row">
                                                            <div class="col-md-6">
                                                                <div>
                                                                    <?php if(isset($discountedProduct->activeDiscount) && $discountedProduct->activeDiscount->discount_percentage>0): ?>
                                                                        <div class="commision_off_wrapper">
                                                                            <h4><?php echo e($discountedProduct->activeDiscount->discount_percentage); ?></h4>
                                                                            <div>
                                                                                <label>%</label>
                                                                                <span>OFF</span>
                                                                            </div>
                                                                        </div>
                                                                    <?php endif; ?>
                                                                </div>
                                                            </div>
                                                            <div class="col-md-6"></div>
                                                        </div>
                                                    </div>
                                                    <div class="product_image_wrapper">
                                                        <a href="<?php echo e(url('product', $discountedProduct->id)); ?>">
                                                            <div class="product_image">
                                                                <img src="<?php echo e(asset('website')); ?>/<?php echo e($discountedProduct->image->image); ?>">
                                                            </div>
                                                        </a>
                                                    </div>
                                                </div>
                                                <div>
                                                    <?php if(isset($discountedProduct->colors) && !empty($discountedProduct->colors)): ?>
                                                        <div class="select_classic_shirts_colors">
                                                            <div class="single_pro_color_slider_wrap">
                                                                <div class="swiper single_pro_color_swiper">
                                                                    <div class="swiper-wrapper">
                                                                        <?php $__currentLoopData = json_decode($discountedProduct->colors); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key => $color): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                                            <div class="swiper-slide">
                                                                                <div class="form-group round_color">
                                                                                    <input type="radio"
                                                                                           style="background-color:<?php echo e($color); ?>;"
                                                                                           id="product_color_<?php echo e($key); ?>"
                                                                                           name="color"
                                                                                           value="<?php echo e($color); ?>" />
                                                                                </div>
                                                                            </div>
                                                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                                    </div>
                                                                    <div class="swiper-button-next single_pro_color_swiper_arrow"></div>
                                                                    <div class="swiper-button-prev single_pro_color_swiper_arrow"></div>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    <?php endif; ?>
                                                    <div class="product_details_wrapper">
                                                        <div class="product_heading_dropdown_wrap">
                                                            <h5><?php echo e(ucwords($discountedProduct->shortName )); ?></h5>
                                                            <div class="dropdown">
                                                                <a class="dropdown-toggle" href="javascript:void(0)" role="button" data-bs-toggle="dropdown" aria-expanded="false"><i class="fa-solid fa-ellipsis-vertical"></i></a>
                                                                <ul class="dropdown-menu">
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('product', $discountedProduct->id)); ?>">Product Detail</a></li>
                                                                    <li><a class="dropdown-item" href="<?php echo e(url('category',$discountedProduct->category->unique)); ?>">Related Products</a></li>
                                                                </ul>
                                                            </div>
                                                        </div>
                                                        <div class="add_to_crt_counter_plus">
                                                            <div class="counter_product_wrap">
                                                                <input class="counter_product_wrap_input" type="number" value="1" readonly>
                                                                <div class="counter_product_wrap_plus_minus">
                                                                    <a type="button" class=" plus_counter_product"><i class="fa-solid fa-caret-up"></i></a>
                                                                    <a type="button" class=" minus_counter_product"><i class="fa-solid fa-caret-down"></i></a>
                                                                </div>
                                                            </div>
                                                            <a href="javascript:void(0)"  class="cart_btn" onclick="showProductDetailModalOrAddToCart(<?php echo e($discountedProduct->id); ?>,this)" ><img src="<?php echo e(asset('website')); ?>/assets/images/shopping-cart.svg">Add to Cart</a>
                                                        </div>
                                                        <div class="product_rating_price_text_wrap">
                                                            <?php echo $__env->make('website.includes.rating', ['product' => $discountedProduct], \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                                            <div class="price_addtocart_wrapper">
                                                                <?php if(isset($discountedProduct->activeDiscount) && $discountedProduct->activeDiscount->discount_percentage>0): ?>
                                                                    <h5><?php echo e($ACTIVE_CURRENCY_SYMBOL); ?><?php echo e(($discountedProduct->price - ($discountedProduct->price * $discountedProduct->activeDiscount->discount_percentage/100))); ?><span><?php echo e($ACTIVE_CURRENCY_SYMBOL); ?><?php echo e($discountedProduct->price); ?></span></h5>
                                                                <?php else: ?>
                                                                    <h5><?php echo e($ACTIVE_CURRENCY_SYMBOL); ?><?php echo e($discountedProduct->price); ?></h5>
                                                                <?php endif; ?>

                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>

                                            </div>
                                        </div>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <?php if(isset($featuredProducts) && $featuredProducts->count()>0): ?>
        <section class="headphone_sec">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-6">
                        <div class="headphone_wrapper">
                            <h4>Featured Product</h4>
                                <?php $featuredRandomProduct = $featuredProducts->random();?>
                            <h2><?php echo e($featuredRandomProduct->shortName); ?></h2>
                            <h4><?php echo e($featuredRandomProduct->category->name); ?> </h4>
                            <div>
                                <p>In Just</p>
                                <h2><?php echo e($ACTIVE_CURRENCY_SYMBOL); ?><?php echo e($featuredRandomProduct->price); ?></h2>
                            </div>
                            <a class="btn_orange" href="<?php echo e(url('product', $featuredRandomProduct->id)); ?>">Shop Now <img src="<?php echo e(asset('website')); ?>/assets/images/Chevron-Down.svg"></a>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="headphone_sec_img">
                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($featuredRandomProduct->image->image); ?>">
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <section class="popular_products_sec">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="popular_products_wrapper">
                        <h3>Popular Products</h3>
                        <div class="popular_products_wrapper">
                            <ul class="nav nav-tabs" id="myTab" role="tablist">
                                <li class="nav-item" role="presentation">
                                    <button class="nav-link active fetch_product_by_category" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-tab-pane" type="button" role="tab" aria-controls="all-tab-pane" aria-selected="true" category_id="all">
                                        All
                                    </button>
                                </li>
                                <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $key=>$category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                    <li class="nav-item" role="presentation">
                                        <button class="nav-link fetch_product_by_category" id="all-tab" data-bs-toggle="tab" data-bs-target="#all-tab-pane" type="button" role="tab" aria-controls="all-tab-pane" aria-selected="true" category_id="<?php echo e($category->id); ?>"> <?php echo e(ucwords($category->name)); ?></button>
                                    </li>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                            </ul>
                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="tab-content" id="myTabContent">
                        <div class="tab-pane fade show active" id="all-tab-pane" role="tabpanel" aria-labelledby="all-tab" tabindex="0">
                            <div class="row custom_row__gap home_page_produtcs_load_ajex" id="product-container">
                                <?php echo $__env->make('website.ajax.products_listing_common', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                            <div id="loader" style="display: none;">
                                <div class="cssload-speeding-wheel">
                                    <div class="loading_icon">
                                        <span><i class="fa-solid fa-circle"></i> </span>
                                        <span><i class="fa-solid fa-circle"></i> </span>
                                        <span><i class="fa-solid fa-circle"></i> </span>
                                    </div>
                                </div>
                            </div>
                            <!-- View More Button -->
                            <?php if(false && $products->count()>10): ?>
                            <div class="view_more_btn_load" id="view-more-wrapper">
                                <button id="view-more-btn" class="cart_btn ">View More <i class="fa-solid fa-chevron-down"></i></button>
                            </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <div class="offcanvas offcanvas-end" tabindex="-1" id="offcanvasRightModal" aria-labelledby="offcanvasRightLabel">
    </div>

<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        var swiper = new Swiper(".hero_swiper", {
            slidesPerView: 1,
            spaceBetween: 30,
            pagination: {
                el: ".swiper-pagination",
            },
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
        $(document).ready(function() {
            let totalSeconds = <?php echo e($discountedProducts->max('activeDiscount.end_date') ? \Carbon\Carbon::parse($discountedProducts->max('activeDiscount.end_date'))->diffInSeconds(now()) : 0); ?>;
            const timerInterval = setInterval(function () {
                if (totalSeconds <= 0) return clearInterval(timerInterval), $('.timer').text('Sale Ended');
                const d = Math.floor(totalSeconds / 86400), h = Math.floor((totalSeconds % 86400) / 3600),
                    m = Math.floor((totalSeconds % 3600) / 60), s = totalSeconds % 60;
                $('.timer').text(`${d > 0 ? d + 'd ' : ''}${h}h ${m}m ${s}s`);
                totalSeconds--;
            }, 1000);
        });
        var swiper = new Swiper(".product-slider", {
            slidesPerView: 4,
            spaceBetween: 20,
            loop: true,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            breakpoints: {
                '1100': {
                    slidesPerView: 4,
                    spaceBetween: 10,
                },
                '1024': {
                    slidesPerView: 3,
                    spaceBetween: 10,
                },
                '991': {
                    slidesPerView: 3,
                    spaceBetween: 10,
                },
                '800': {
                    slidesPerView: 3,
                    spaceBetween: 10,
                },
                '600': {
                    slidesPerView: 2,
                    spaceBetween: 10,
                },
                '320': {
                    slidesPerView: 1,
                    spaceBetween: 10,
                },
            }
        });
        var swiper = new Swiper(".category_slider", {
            slidesPerView: 6,
            spaceBetween: 20,
            autoplay: {
                delay: 3000,
            },

            loop: true,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
            pagination: {
                el: ".swiper-pagination",
                dynamicBullets: true,

            },
            breakpoints: {
                '1280': {
                    slidesPerView: 6,
                    spaceBetween: 10,
                },
                '1100': {
                    slidesPerView: 5,
                    spaceBetween: 10,
                },


                '800': {
                    slidesPerView: 4,
                    spaceBetween: 10,
                },
                '600': {
                    slidesPerView: 3,
                    spaceBetween: 10,
                },
                '320': {
                    slidesPerView: 2,
                    spaceBetween: 10,
                },
            }
        });
        $(document).on('click','.add_to_wishlist',function() {
            var productId = $(this).data('product-id');
            var $this = $(this);
            $.ajax({
                url: '<?php echo e(url('add-to-wishlist')); ?>',
                type: 'POST',
                data: { _token: '<?php echo e(csrf_token()); ?>', id: productId },
                success: function(response) {
                    swal.fire({
                        'title': response.title,
                        'text' : response.message,
                        'icon' : response.type,
                    });

                    $('.get_wishlist_items_count').text(response.wishlistCount)
                    if (response.type == 'success'){
                        $('.wishlist-item_'+productId).toggleClass('clicked');
                    }

                }
            });
        });

        var category = 'all';
        var page = 2;
        var last = true;
        var loading = false;
        var hasNextPage = true;
        $(document).ready(function() {
            function loadMoreProducts() {
                if (loading || !hasNextPage) return;
                loading = true;

                $('#loader').show();

                $.ajax({
                    url: '<?php echo e(url()->current()); ?>?page=' + page + '&category=' + category,
                    type: 'GET',
                    success: function (response) {
                        if (response.products.length > 0) {
                            $('#product-container').append(response.products);
                            page++;
                            last = true;
                            initializeSwiper(); // if you use Swiper
                        } else {
                            last = false;
                            $('#view-more-wrapper').hide(); // hide button when no more products
                        }
                        $('#loader').hide();
                        loading = false;
                    },
                    error: function () {
                        $('#loader').hide();
                        loading = false;
                    }
                });
            }

            function onScroll() {
                // Get the distance from the bottom of the page to the footer
                var footerOffset = $('.footer').offset().top;
                var windowHeight = $(window).height();
                var scrollTop = $(window).scrollTop();

                // Check if the footer is within 50px from the bottom of the page
                if (footerOffset - scrollTop <= windowHeight + 50 && !loading && hasNextPage) {
                    loadMoreProducts();
                }
            }

            $(window).on('scroll', onScroll);
        });

        // Category filter click handler
        $(document).on('click', '.fetch_product_by_category', function () {
            $('#loader').show();
            category = $(this).attr('category_id');
            page = 2; // reset page number
            last = category === 'all' ? true : false;
            $('#searched-items-tab-li').hide();
            getProductsByCategoryAndProductId(category, null);

            $('#view-more-wrapper').show(); // ensure button is visible on category change
        });

        $(document).ready(function() {
            $('.send_msg_button').click(function() {
                var userMessage = $('.message_input').val().trim();
                if (userMessage !== "") {
                    appendUserMessage(userMessage);
                    $('.message_input').val('');
                    setTimeout(function() {
                        var chatbotMessage = "This is the chatbot's response";
                        appendChatbotMessage(chatbotMessage);
                    }, 1000);

                    $('.chat_body').scrollTop($('.chat_body')[0].scrollHeight);
                }
            });
            $('.message_input').keypress(function(e) {
                if (e.which == 13) {
                    $('.send_msg_button').click();
                }
            });

            function appendUserMessage(message) {
                var newMessage = $('<div class="user_message"></div>');
                newMessage.html('<p>' + message + '</p>');
                $('.chat_body').append(newMessage);
            }
            function appendChatbotMessage(message) {
                var newMessage = $('<div class="chatbot_message"></div>');
                newMessage.html('<p>' + message + '</p>');
                $('.chat_body').append(newMessage);
            }
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('website.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/website/index.blade.php ENDPATH**/ ?>