<?php $__env->startPush('css'); ?>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="login_sec">
        <div class="container-fluid">
            <!--begin::Card-->
            <div class="row custom_row_login">
                <!--begin::Wrapper-->
                <div class="col-md-12">
                    <div class="login_sign_up_wrapper">
                        <form class="form w-100" novalidate="novalidate" method="POST" action="<?php echo e(route('login')); ?>">
                            <?php echo csrf_field(); ?>
                            <div class="login_details_wrapper">
                                <div class="login_fileds_wrapper">
                                    <div class="login_fileds_wrapper_inner">
                                        <div class="log_in_to_execlusive">
                                            <h4>Enter your details below</h4>
                                        </div>
                                        <div class="login_input_wrapper">
                                            <input id="email" type="text" placeholder="Email or Phone Number" class="form-control <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="email" value="<?php echo e(old('email')); ?>" required autocomplete="email" autofocus>
                                            <small class="form-text text-muted">You can log in using your email or phone number.</small>
                                            <?php $__errorArgs = ['email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert"><strong><?php echo e($message); ?></strong></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="login_input_wrapper input_wrapper">
                                            <input id="password" type="password" placeholder="Password" class="form-control pass_log <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" name="password" required autocomplete="current-password">
                                            <button type="button" class="btn btn-link password-toggle" aria-label="Toggle password visibility">
                                                <i class="fa-regular input_icon fa-eye d-none" aria-hidden="true"></i>
                                                <i class="fa-regular input_icon fa-eye-slash " aria-hidden="true"></i>
                                            </button>
                                            <small class="form-text text-muted">Password must be at least 6 characters.</small>
                                            <?php $__errorArgs = ['password'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                            <span class="invalid-feedback" role="alert"><strong><?php echo e($message); ?></strong></span>
                                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                                        </div>
                                        <div class="login_submit_wrapper">
                                            <button type="submit" id="kt_sign_in_submit" class="cart_btn">
                                                <span class="indicator-label">Log Me In</span>
                                                <span class="indicator-progress">Please wait...
<span class="spinner-border spinner-border-sm "></span></span>
                                            </button>
                                        </div>
                                        <div class="forget_password">
                                            <a href="<?php echo e(route('password.request')); ?>" class="">Forgot Password?</a>
                                        </div>
                                        <div class="forget_password sign_up_wrapper">
                                            <h5>Don't have an account?</h5>
                                            <a href="<?php echo e(route('register')); ?>" class="">Sign Up</a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                    
                    <!--end::Form-->
                </div>
                <!--end::Wrapper-->
            </div>
            <!--end::Card-->
        </div>
        <!--end::Body-->
    </section>
    <!--end::Authentication - Sign-in-->
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    <script>
        $(document).ready(function() {
            $(".form").validate({
                rules: {
                    email: {
                        required: true,
                        emailOrPhone: true
                    },
                    password: {
                        required: true,
                        minlength: 6
                    }
                },
                messages: {
                    email: {
                        required: "Email or phone is required",
                        emailOrPhone: "Enter a valid email or phone number"
                    },
                    password: {
                        required: "Password is required",
                        minlength: "Password must be at least 6 characters"
                    }
                },
                errorElement: 'span',
                errorClass: 'text-danger',
                highlight: function(element) {
                    $(element).addClass("is-invalid").removeClass("is-valid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid").addClass("is-valid");
                },
                submitHandler: function(form) {
                    form.submit();
                }
            });
            $.validator.addMethod("emailOrPhone", function(value, element) {
                let emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                let phonePattern = /^\+?\d{7,15}$/;
                return this.optional(element) || emailPattern.test(value) || phonePattern.test(value);
            }, "Please enter a valid email or phone number");
        });
    </script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/auth/login.blade.php ENDPATH**/ ?>