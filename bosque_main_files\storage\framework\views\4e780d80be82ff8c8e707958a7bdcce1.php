<div class="app-navbar header_sec ">
    <div class="dasgboard_logo_header-Wrapper">
        <div class="header_logo_img desktop_logo">
            <a href="<?php echo e(url('/')); ?>">
                <img alt="Logo" src="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->logo??''); ?>">
            </a>
        </div>
    </div>
    <div class="navbar_pages_wrapper">
        <button class="custom_hamburg d-lg-none"><i class="fa-solid fa-bars"></i></button>
        <div id="responsiveMenu" class="d-none d-lg-block">
        <?php if(auth()->user()->hasRole('admin')): ?>
            <ul class="navbar-nav bg-light">
                <li class="nav-item <?php if(request()->route()->getName() == "dashboard" || request()->route()->getName() == "dashboard" || request()->route()->getName() == "dashboard"): ?> active <?php endif; ?>">
                    <a class="nav-link " href="<?php echo e(url('dashboard')); ?> " aria-current="page"><i class="fa-solid fa-house"></i>Dashboard</a>
                </li>
                <li class="nav-item dropdown dropdown_inenr_wrapper <?php if(request()->is('products*') || request()->is('categories*')): ?> active <?php endif; ?>">
                    <a data-bs-toggle="dropdown" aria-expanded="false" class="dropdown-toggle nav-link" href="javascript:void(0)" aria-current="page"><i class="fa-solid fa-tools"></i>Platform Management</a>
                    <ul class="dropdown-menu inner_dropdwon">
                        <li class="<?php if(request()->is('categories*')): ?> active <?php endif; ?>"><a href="<?php echo e(url('categories')); ?>" class="dropdown-item">Categories</a></li>
                        <li class="<?php if(request()->is('products*')): ?> active <?php endif; ?>"><a href="<?php echo e(url('products')); ?>" class="dropdown-item" >Products</a></li>
                        <li class="<?php if(request()->is('content-managements*')): ?> active <?php endif; ?>"><a href="<?php echo e(url('content-managements')); ?>" class="dropdown-item" >CMS</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown dropdown_inenr_wrapper <?php if(request()->is('users*')): ?> active <?php endif; ?>">
                    <a data-bs-toggle="dropdown" aria-expanded="false" class="dropdown-toggle nav-link" href="javascript:void(0)" aria-current="page"><i class="fa-solid fa-users"></i>Users</a>
                    <ul class="dropdown-menu inner_dropdwon">
                        <li class="<?php if(request()->is('users') && request()->query('type') === 'customer'): ?> active <?php endif; ?>"><a href="<?php echo e(url('users?type=customer')); ?>" class="dropdown-item">Customers</a></li>
                        <li class="<?php if(request()->is('users') && request()->query('type') === 'seller'): ?> active <?php endif; ?>"><a href="<?php echo e(url('users?type=seller')); ?>" class="dropdown-item" >Sellers</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown dropdown_inenr_wrapper <?php if(request()->route()->getName() == "funds" || request()->route()->getName() == "withdrawal-request" || request()->route()->getName() == "funds"): ?> active <?php endif; ?>">
                    <a data-bs-toggle="dropdown" aria-expanded="false"  class="dropdown-toggle nav-link" href="javascript:void(0)" aria-current="page"><i class="fa-regular fa-file-lines"></i>Funds</a>
                    <ul class="dropdown-menu inner_dropdwon">
                        <li class="<?php if(request()->route()->getName() == "funds" || request()->route()->getName() == ""): ?> active <?php endif; ?>"><a href="<?php echo e(url('funds')); ?>" class="dropdown-item">Funds</a></li>
                        <li class="<?php if(request()->route()->getName() == "withdrawal-request" || request()->route()->getName() == ""): ?> active <?php endif; ?>"><a href="<?php echo e(url('withdrawal-request')); ?>" class="dropdown-item" >Withdrawal Request</a></li>
                    </ul>
                </li>
                <li class="nav-item dropdown dropdown_inenr_wrapper <?php if(request()->route()->getName() == "customer-inventory-details" || request()->route()->getName() == "order-inventory-details" || request()->route()->getName() == "inventory-details" || request()->route()->getName() == "inventory-reports" || request()->route()->getName() == "sales-reports" || request()->route()->getName() == "order-reports" || request()->route()->getName() == "customer-reports" || request()->route()->getName() == "sales-inventory-details"): ?> active <?php endif; ?>">
                    <a data-bs-toggle="dropdown" aria-expanded="false" class="dropdown-toggle nav-link" href="javascript:void(0)" aria-current="page"><i class="fa-solid fa-sliders"></i>Reports</a>
                    <ul class="dropdown-menu inner_dropdwon">
                        <li class="<?php if(request()->route()->getName() == "inventory-reports" || request()->route()->getName() == "inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('inventory-reports')); ?>" class="dropdown-item">Inventory Reports</a></li>
                        <li class="<?php if(request()->route()->getName() == "sales-reports" || request()->route()->getName() == "sales-inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('sales-reports')); ?>" class="dropdown-item" >Sales Reports</a></li>
                        <li class="<?php if(request()->route()->getName() == "order-reports" || request()->route()->getName() == "order-inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('order-reports')); ?>" class="dropdown-item" >Order Reports</a></li>
                        <li class="<?php if(request()->route()->getName() == "customer-reports" || request()->route()->getName() == "customer-inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('customer-reports')); ?>" class="dropdown-item" >Customer Reports</a></li>
                    </ul>
                </li>
                <li class="nav-item <?php if(request()->route()->getName() == "recent-orders" || request()->route()->getName() == "recent-order-inventory-details"): ?> active <?php endif; ?>">
                    <a class="nav-link" href="<?php echo e(url('recent-orders')); ?>" aria-current="page"><i class="fa-regular fa-star"></i>Reviews</a>
                </li>
            </ul>
        <?php elseif(auth()->user()->hasRole('seller')): ?>
            <ul class="navbar-nav">
                <li class="nav-item <?php if(request()->is('dashboard*')): ?> active <?php endif; ?>">
                    <a class="nav-link " href="<?php echo e(url('dashboard')); ?> " aria-current="page"><i class="fa-solid fa-house"></i>Dashboard</a>
                </li>
                <li class="nav-item <?php if(request()->is('products*')): ?> active <?php endif; ?>">
                    <a class="nav-link " href="<?php echo e(url('products')); ?> " aria-current="page"><i class="fa-solid fa-users"></i>Product Catalog</a>
                </li>
                <li class="nav-item <?php if(request()->is('orders*') || request()->route()->getName() == "recent-order-inventory-details" || request()->route()->getName() == ""): ?> active <?php endif; ?>">
                    <a class="nav-link " href="<?php echo e(url('orders')); ?> " aria-current="page"><i class="fa-regular fa-file-lines"></i>Orders</a>
                </li>
                <li class="nav-item dropdown dropdown_inenr_wrapper <?php if(request()->route()->getName() == "" || request()->route()->getName() == "order-inventory-details" || request()->route()->getName() == "inventory-details" || request()->route()->getName() == "inventory-reports" || request()->route()->getName() == "sales-reports" || request()->route()->getName() == "order-reports" || request()->route()->getName() == "" || request()->route()->getName() == "sales-inventory-details"): ?> active <?php endif; ?>">
                    <a data-bs-toggle="dropdown" aria-expanded="false" class="dropdown-toggle nav-link" href="javascript:void(0)" aria-current="page"><i class="fa-solid fa-sliders"></i>Reports<i class="fa-solid fa-chevron-down"></i></a>
                    <ul class="dropdown-menu inner_dropdwon">
                        <li class="<?php if(request()->route()->getName() == "inventory-reports" || request()->route()->getName() == "inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('inventory-reports')); ?>" class="dropdown-item">Inventory Reports</a></li>
                        <li class="<?php if(request()->route()->getName() == "sales-reports" || request()->route()->getName() == "sales-inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('sales-reports')); ?>" class="dropdown-item" >Sales Reports</a></li>
                        <li class="<?php if(request()->route()->getName() == "order-reports" || request()->route()->getName() == "order-inventory-details"): ?> active <?php endif; ?>"><a href="<?php echo e(url('order-reports')); ?>" class="dropdown-item" >Order Reports</a></li>
                        
                    </ul>
                </li>
                <li class="nav-item <?php if(request()->route()->getName() == "payments" || request()->route()->getName() == "Payment-inventory-details" || request()->route()->getName() == ""): ?> active <?php endif; ?>">
                    <a class="nav-link " href="<?php echo e(url('payments')); ?> " aria-current="page"><i class="fa-solid fa-money-check-dollar"></i>Payment</a>
                </li>
            </ul>
        <?php endif; ?>
    </div>
    </div>

    <div class="dasgboard_logo_header-Wrapper responsive_logo">
        <div class="header_logo_img">
            <a href="<?php echo e(url('/')); ?>">
                <img src="<?php echo e(asset('website')); ?>/assets/images/logo_black.png">
            </a>
        </div>
    </div>

    <div class="logo_search_notification_wrapper">
        <div class="wallet_container">
            <a href="javascript:void(0)" class="searchbar_header_wrapper wallet_dropdown">
               <img src="<?php echo e(asset('website')); ?>/assets/images/e_wallet_header.svg">
            </a>
            <div  class="header_wallet_dropdown">
                <h2>E-Wallet</h2>
                <div class="custom_justify">
                    <div class="custom_flex">
                        <p>Total Balance</p>
                        <h2><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e(number_format(Auth::user()->wallet->balance ?? 0, 2)); ?></h2>
                    </div>
                        <a href="<?php echo e(url('withdrawal-request')); ?>" class="btn_maroon">Withdrawal Requests<img src="<?php echo e(asset('website')); ?>/assets/images/circle-plus.svg" alt=""></a>
                </div>
            </div>
        </div>
        <div class="app-navbar-item">
            <!--begin::Menu wrapper-->
            <div class="btn btn-icon btn-custom notification_icon"
                 data-kt-menu-trigger="click"  data-kt-menu-attach="parent"
                 data-kt-menu-placement="bottom-end" id="kt_menu_item_wow">
                <span class="custom_icon"><i class="fa-solid fa-bell"></i></span>
                <span class="custom_badge"><?php echo e(auth()->user()->notifications->where('read_at',null)->count()??0); ?></span>
            </div>
            <div class="menu menu-sub menu-sub-dropdown menu-column notification_wrapper custom_notification" data-kt-menu="true"  id="kt_menu_notifications" class="nav_notify">
                <div class="box_shadow_wrapper notification_pg_wrapper">
                    <div class="mark_all_read_btn">
                        <h2>Notifications</h2>
                        <a href="<?php echo e(url('notifications')); ?>" class="view_all_header">View All</a>
                    </div>
                    <?php $__currentLoopData = auth()->user()->notifications->where('read_at',null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $notification): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                        <a href="<?php echo e(isset($notification->data['redirect_url']) ? url($notification->data['redirect_url']) : 'javascript:void(0)'); ?>">
                        <div class="notification_user_det_wrap">
                            <div class="notification_img_and_title">
                                <div class="client_noti_img_wrap_upper">
                                    <div class="notifications_img_whole_wrapper">
                                        <div class="client_noti_img_wrap">
                                            <img src="<?php echo e(asset('website/assets/images/table_img_name.svg')); ?>">
                                        </div>
                                    </div>
                                </div>
                                <div class="notification_client_name_det">
                                    <?php echo e($notification->data['name'] ?? 'Unknown User'); ?>

                                    <p><?php echo e($notification->data['message']); ?></p>
                                </div>
                            </div>
                            <div class="notification_client_time_wrap">
                                <i class="fa-solid fa-circle"></i>
                                <h6><?php echo e($notification->created_at->diffForHumans()); ?></h6>
                            </div>
                        </div>
                        </a>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                </div>
            </div>
        </div>
        <!--begin::User menu-->
        <div class="app-navbar-item header_user_img_wrapper" id="kt_header_user_menu_toggle" data-kt-menu-trigger="{default: 'click', lg: 'hover'}">
            <!--begin::Menu wrapper-->
            <div class="cursor-pointer symbol  navbar_image_wrapper"
                 data-kt-menu-attach="parent" data-kt-menu-placement="bottom-end">
                <div class="profile_image">
                    <?php if(auth()->user()->profile->pic == null): ?>
                        <img src="<?php echo e(asset('website/users/no_avatar.jpg')); ?>" class="rounded-3" alt="user" />
                    <?php else: ?>
                        <img src="<?php echo e(asset('website')); ?>/<?php echo e(auth()->user()->profile->pic); ?>" class="rounded-3"
                             alt="user" />
                    <?php endif; ?>
                </div>
            </div>
            <div class="dropdown header_username_dropdown">
                <a href="#!" class="dropdown-toggle"  data-bs-toggle="dropdown" aria-expanded="false">
                    <div>
                        <h5><?php echo e(Auth::user()->name??''); ?>  <i class="fa-solid fa-chevron-down"></i></h5>
                        <p><?php echo e(Auth::user()->roles->first()->name??''); ?></p>
                    </div>

                </a>
                <ul class="dropdown-menu inner_dropdwon">
                    <li><a class="dropdown-item" href="<?php echo e(url('users',auth()->user()->unique)); ?>/edit">Profile Settings</a></li>
                    <li><a class="dropdown-item" href="<?php echo e(url('logout')); ?>">Logout</a></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/theme/layout/right_sidebar.blade.php ENDPATH**/ ?>