

<?php $__env->startPush('css'); ?>
    <style>
        .discount-step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #6f42c1;
            margin-bottom: 20px;
        }
        .discount-step h5 {
            color: #495057;
            font-weight: 600;
        }
        .product-selection-step {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            border-left: 4px solid #28a745;
        }
        .product-selection-step h5 {
            color: #495057;
            font-weight: 600;
        }
        .table-responsive {
            border: 1px solid #dee2e6;
            border-radius: 6px;
            max-height: 500px;
            overflow-y: auto;
        }
        .product_name_wrapper {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .product_img img {
            width: 40px;
            height: 40px;
            object-fit: cover;
            border-radius: 4px;
        }
        .product_name_details h6 {
            margin: 0;
            font-size: 14px;
            font-weight: 500;
        }
        .product_name_details p {
            margin: 0;
            font-size: 12px;
            color: #6c757d;
        }
        #submit-discount:disabled {
            opacity: 0.6;
            cursor: not-allowed;
        }
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('content'); ?>
    <section class="dashboard_index_pg_sec product_catalog">
        <div class="container custom_container">
            <div class="row custom_row_gap">
                <div class="col-md-12">
                    <div class="box_shadow_wrapper">
                        <div class="reviews_chart_wrapper">
                            <h2><i class="fa-solid fa-tag text-warning"></i> Add Discount to Products</h2>
                            <div class="custom_flex">
                                <a href="<?php echo e(route('products-discount.index')); ?>" class="btn_light">
                                    <i class="fa-solid fa-arrow-left"></i> Back to Discounts
                                </a>
                            </div>
                        </div>

                    <form action="<?php echo e(route('products-discount.store')); ?>" method="POST">
                        <?php echo csrf_field(); ?>

                        <!-- Step 1: Discount Details -->
                        <div class="discount-step">
                            <h5 class="mb-3"><i class="fa-solid fa-1 text-primary"></i> Discount Details</h5>
                            <div class="row">
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="discount_percentage" class="form-label">Discount Percentage <span class="text-danger">*</span></label>
                                        <div class="input-group">
                                            <input type="number" class="form-control product_discount_percentage"
                                                   id="discount_percentage" name="discount_percentage"
                                                   placeholder="10" min="0.01" max="100" step="0.01" required>
                                            <span class="input-group-text">%</span>
                                        </div>
                                        <small class="text-muted">Enter value between 0.01% and 100%</small>
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="start_date" name="start_date"
                                               required min="<?php echo e(date('Y-m-d')); ?>" value="<?php echo e(date('Y-m-d')); ?>">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-group">
                                        <label for="end_date" class="form-label">End Date <span class="text-danger">*</span></label>
                                        <input type="date" class="form-control" id="end_date" name="end_date"
                                               required min="<?php echo e(date('Y-m-d')); ?>">
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Step 2: Select Products -->
                        <div class="product-selection-step">
                            <h5 class="mb-3"><i class="fa-solid fa-2 text-primary"></i> Select Products</h5>

                            <div class="row mb-3">
                                <div class="col-md-8">
                                    <div class="input-group">
                                        <span class="input-group-text"><i class="fa-solid fa-search"></i></span>
                                        <input type="text" class="form-control" id="modal_product_search"
                                               placeholder="Search products by name or SKU...">
                                    </div>
                                </div>
                                <div class="col-md-4">
                                    <div class="form-check">
                                        <input type="checkbox" class="form-check-input" id="select_all_products">
                                        <label class="form-check-label" for="select_all_products">
                                            <strong>Select All Products</strong>
                                        </label>
                                    </div>
                                </div>
                            </div>

                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead class="table-light sticky-top">
                                        <tr>
                                            <th width="50">Select</th>
                                            <th>Product</th>
                                            <th width="100">Price</th>
                                            <th width="80">Stock</th>
                                            <th width="120">Discount Qty</th>
                                        </tr>
                                    </thead>
                                    <tbody id="discount-products-tbody">
                                        <!-- Products loaded via AJAX -->
                                    </tbody>
                                </table>
                            </div>

                            <div class="text-center mt-3">
                                <button type="button" class="btn btn-outline-primary" id="load_more_products" style="display: none;">
                                    <i class="fa-solid fa-plus"></i> Load More Products
                                </button>
                            </div>

                            <div class="alert alert-info mt-3" id="selection-info" style="display: none;">
                                <i class="fa-solid fa-info-circle"></i>
                                <span id="selected-count">0</span> product(s) selected for discount
                            </div>
                        </div>

                        <!-- Action Buttons -->
                        <div class="row mt-4">
                            <div class="col-md-12">
                                <div class="d-flex justify-content-end gap-2">
                                    <a href="<?php echo e(route('products-discount.index')); ?>" class="btn btn-secondary">
                                        <i class="fa-solid fa-times"></i> Cancel
                                    </a>
                                    <button type="submit" class="btn btn-success" id="submit-discount" disabled>
                                        <i class="fa-solid fa-tag"></i> Apply Discount
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    </div>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
<script>
    $(document).ready(function() {
        let currentPage = 1;
        let isLoading = false;
        let hasMorePages = true;

        // Load initial products
        loadProducts();

        function loadProducts(search = '', page = 1) {
            if (isLoading) return;

            isLoading = true;
            $('#load_more_products').text('Loading...').prop('disabled', true);

            $.ajax({
                url: '<?php echo e(route("products-discount.get-products")); ?>',
                method: 'GET',
                data: {
                    search: search,
                    page: page
                },
                success: function(response) {
                    if (page === 1) {
                        $('#discount-products-tbody').empty();
                    }

                    if (response.data && response.data.length > 0) {
                        response.data.forEach(function(product) {
                            const row = `
                                <tr>
                                    <td>
                                        <input type="checkbox" class="form-check-input select_product"
                                               name="product_ids[]" value="${product.id}">
                                    </td>
                                    <td>
                                        <div class="product_name_wrapper">
                                            <div class="product_img">
                                                <img src="${product.image}" alt="Product Image">
                                            </div>
                                            <div class="product_name_details">
                                                <h6>${product.name}</h6>
                                                <p>SKU: ${product.sku_id}</p>
                                            </div>
                                        </div>
                                    </td>
                                    <td>£${product.price}</td>
                                    <td>${product.stock}</td>
                                    <td>
                                        <input type="number" class="form-control form-control-sm discount_quantity"
                                               name="discount_quantities[${product.id}]"
                                               placeholder="Qty" min="1" max="${product.stock}"
                                               style="width: 80px;" disabled>
                                    </td>
                                </tr>
                            `;
                            $('#discount-products-tbody').append(row);
                        });

                        hasMorePages = response.has_more_pages;
                        currentPage = page;

                        if (hasMorePages) {
                            $('#load_more_products').show().text('Load More Products').prop('disabled', false);
                        } else {
                            $('#load_more_products').hide();
                        }
                    } else {
                        if (page === 1) {
                            $('#discount-products-tbody').html('<tr><td colspan="5" class="text-center">No products found</td></tr>');
                        }
                        $('#load_more_products').hide();
                    }
                },
                error: function() {
                    Swal.fire('Error', 'Failed to load products', 'error');
                },
                complete: function() {
                    isLoading = false;
                }
            });
        }

        // Search functionality
        $('#modal_product_search').on('keyup', function() {
            const search = $(this).val();
            currentPage = 1;
            hasMorePages = true;
            loadProducts(search, 1);
        });

        // Load more products
        $('#load_more_products').on('click', function() {
            if (hasMorePages) {
                loadProducts($('#modal_product_search').val(), currentPage + 1);
            }
        });

        // Select All logic with improved feedback
        $('#select_all_products').on('change', function () {
            $('.select_product').prop('checked', this.checked).trigger('change');
            updateSelectionInfo();
        });

        $(document).on('change', '.select_product', function () {
            const total = $('.select_product').length;
            const checked = $('.select_product:checked').length;
            $('#select_all_products').prop('checked', total === checked);

            // Enable/disable quantity input
            const quantityInput = $(this).closest('tr').find('.discount_quantity');
            if ($(this).is(':checked')) {
                quantityInput.prop('disabled', false).focus();
            } else {
                quantityInput.prop('disabled', true).val('');
            }

            updateSelectionInfo();
        });

        // Update selection info
        function updateSelectionInfo() {
            const selectedCount = $('.select_product:checked').length;
            $('#selected-count').text(selectedCount);

            if (selectedCount > 0) {
                $('#selection-info').show();
                $('#submit-discount').prop('disabled', false);
            } else {
                $('#selection-info').hide();
                $('#submit-discount').prop('disabled', true);
            }
        }

        // Form submission with improved validation
        $('form').on('submit', function (e) {
            let isValid = true;
            $('.validation-error').remove();

            // Check if products are selected
            if ($('.select_product:checked').length === 0) {
                isValid = false;
                Swal.fire({
                    icon: 'warning',
                    title: 'No Products Selected',
                    text: 'Please select at least one product to apply the discount.',
                    confirmButtonColor: '#6f42c1'
                });
                return false;
            }

            // Validate discount quantities
            let hasInvalidQuantity = false;
            $('.select_product:checked').each(function() {
                const row = $(this).closest('tr');
                const quantityInput = row.find('.discount_quantity');
                const quantity = parseInt(quantityInput.val());
                const maxStock = parseInt(quantityInput.attr('max'));

                if (!quantity || quantity < 1 || quantity > maxStock) {
                    hasInvalidQuantity = true;
                    quantityInput.addClass('is-invalid');
                } else {
                    quantityInput.removeClass('is-invalid');
                }
            });

            if (hasInvalidQuantity) {
                isValid = false;
                Swal.fire({
                    icon: 'error',
                    title: 'Invalid Quantities',
                    text: 'Please enter valid quantities for all selected products.',
                    confirmButtonColor: '#6f42c1'
                });
                return false;
            }

            if (!isValid) {
                e.preventDefault();
            }
        });

        // Initialize selection info
        updateSelectionInfo();
    });
</script>
<?php $__env->stopPush(); ?>
<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/productdiscounts/create.blade.php ENDPATH**/ ?>