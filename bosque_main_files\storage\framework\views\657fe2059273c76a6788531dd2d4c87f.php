<!DOCTYPE html>
<html lang="en">
<head>
    <base href="" />
    <title><?php echo e(@ucwords(str_replace('_',' ',str_replace('-',' ',@Request::segment(1)??App\Models\Setting::first()->title??'')))); ?></title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
    <meta charset="utf-8" />
    <meta name="description" content="" />
    <meta name="keywords" content="" />
    <meta name="viewport" content="width=device-width, initial-scale=1" />
    <meta property="og:locale" content="en_US" />
    <meta property="og:type" content="article" />
    <meta property="og:title" content="" />
    <meta property="og:url" content="" />
    <meta property="og:site_name" content="" />
    <link rel="canonical" href="" />
    <link rel="shortcut icon" href="<?php echo e(asset('')); ?><?php echo e(App\Models\Setting::first()->favicon??''); ?>" />
    <link rel="stylesheet" href="https://fonts.googleapis.com/css?family=Inter:300,400,500,600,700" />
    <link href="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('website')); ?>/assets/css/style.bundle.css" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('css/dashboard.css')); ?>" rel="stylesheet" />
    <link href="<?php echo e(asset('css/dashboard_responsive.css')); ?>" rel="stylesheet" />
    <?php echo $__env->yieldPushContent('css'); ?>
</head>
<body id="kt_app_body" data-kt-app-layout="dark-sidebar" data-kt-app-header-fixed="true"
    data-kt-app-sidebar-enabled="true" data-kt-app-sidebar-fixed="true" data-kt-app-sidebar-hoverable="true"
    data-kt-app-sidebar-push-header="true" data-kt-app-sidebar-push-toolbar="true"
    data-kt-app-sidebar-push-footer="true" data-kt-app-toolbar-enabled="true" class="app-default">
    <div class="preloader">
        <div class="cssload-speeding-wheel">
            <div class="loader_img">
                <img src="<?php echo e(asset('website')); ?>/assets/images/dashboard_logo.png">
            </div>
            <div class="loading_icon">
                <span><i class="fa-solid fa-circle"></i> </span>
                <span><i class="fa-solid fa-circle"></i> </span>
                <span><i class="fa-solid fa-circle"></i> </span>
            </div>
        </div>
    </div>
    <div class=" flex-root app-root" id="kt_app_root">
        <div class="" id="kt_app_page">
            <div id="kt_app_header" class="app-header mini_sidebar" data-kt-sticky="true" data-kt-sticky-activate="{default: true, lg: true}" data-kt-sticky-name="app-header-minimize" data-kt-sticky-offset="{default: '200px', lg: '0'}" data-kt-sticky-animation="false">
                <div class="container custom_container" id="kt_app_header_container">
                    <!-- Sidebar Wrapper -->
                    <div id="kt_app_header_wrapper" class="mini-sidebar">
                        <?php echo $__env->make('theme.layout.right_sidebar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                    </div>
                </div>
            </div>
            <div class="app-wrapper app_wrapper_custom " id="kt_app_wrapper">
                <div class="app-main" id="kt_app_main">
                    <?php echo $__env->yieldContent('breadcrumb'); ?>
                    <?php echo $__env->yieldContent('content'); ?>
                </div>
                <div id="kt_app_footer" class="app-footer">
                    <div class="container custom_container">
                        <div class="reserved_and_terms_pg_wrapper">
                            <p>© <?php echo e(date('Y')); ?> Bosque. All right reserved</p>
                            <div class="footer_terms_condition_wrapper">
                                <a href="<?php echo e(url('privacy-policy')); ?>">Privacy Policy</a>
                                <a href="<?php echo e(url('terms-conditions')); ?>">Terms & Conditions</a>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
    </div>
   <?php echo $__env->make('theme.layout.modal', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <script>
        var hostUrl = "<?php echo e(asset('website')); ?>/assets/";
    </script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/global/plugins.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/js/scripts.bundle.js"></script>
    <script src="<?php echo e(asset('website')); ?>/assets/plugins/custom/datatables/datatables.bundle.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/sweetalert2@11"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jquery-validate/1.21.0/jquery.validate.min.js" ></script>
    <script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('GOOGLE_MAP_API_KEY')); ?>&libraries=places"></script>
    <script type="text/javascript">
        <?php if(session()->has('message')): ?>
            Swal.fire({
                title: "<?php echo e(session()->get('title') ?? 'success!'); ?>",
                html: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>
        <?php if(session()->has('flash_message')): ?>
            Swal.fire({
                title: "<?php echo e(@ucwords(preg_replace('/(?<!\ )[A-Z]/', ' $0', session()->get('flash_message')))); ?>",
                icon: "<?php echo e(session()->get('type') ?? 'success'); ?>",
                timer: 5000,
                buttons: false,
            });
        <?php endif; ?>
        //delete button confirm swal dynamic.
        function showDeleteConfirmation(button) {
            Swal.fire({
                title: 'Confirm Deletion',
                text: 'Are you sure you want to delete this item? This action cannot be undone.',
                showCancelButton: true,
                confirmButtonColor: '#C50000',
                cancelButtonColor: '#F3EFEC',
                cancelButtonText: 'Cancel <i class="fa-solid fa-xmark"></i>',
                confirmButtonText: 'Yes <i class="fa-solid fa-check"></i>'
            }).then((result) => {
                if (result.isConfirmed) {
                    button.closest('.delete-form').submit();
                }
            });
        }

        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $(document).ready(function() {
            var dataTable = $('.myTable'). DataTable({
                "searching": true,
                "bLengthChange": false,
                "paging": true,
                "info": true,
                "stateSave": true,
            });
            $(document).on("input", '.custom_search_box', function () {
                var searchValue = $(this).val();
                dataTable.search(searchValue).draw();
            });
            $(".myTable").DataTable();
        })
        $(document).ready(function () {
            $('.wallet_dropdown').click(function (e) {
                e.stopPropagation(); // Stop click from bubbling to document
                $('.header_wallet_dropdown').toggle();

                if ($('.header_wallet_dropdown').is(':visible')) {
                    $('.wallet_container').addClass('wallet_remove');
                    $('.logo_search_notification_wrapper .inner_dropdwon').removeClass('show');
                    $('.notification_wrapper').removeClass('show');
                } else {
                    $('.wallet_container').removeClass('wallet_remove');
                }
            });

            $('.header_wallet_dropdown').click(function (e) {
                e.stopPropagation();
            });

            $(document).click(function () {
                $('.header_wallet_dropdown').hide();
                $('.wallet_container').removeClass('wallet_remove');
            });
        });


        $(window).on('load', function() {
            setTimeout(function() {
                $('.preloader').css('visibility', 'hidden');
                $('#kt_app_body').css('display', 'inline');
            }, 200);
        });
        function showRedirectionConfirmation(event, url, message) {
            event.preventDefault();
            Swal.fire({
                title: 'Are you sure?',
                text: message,
                icon: 'warning',
                showCancelButton: true,
                confirmButtonColor: '#3085d6',
                cancelButtonColor: '#d33',
                confirmButtonText: 'Yes',
                cancelButtonText: 'Cancel'
            }).then((result) => {
                if (result.isConfirmed) {
                    window.location.href = url;
                }
            });
        }

        $(document).ready(function () {
            var table = $('.datatable').DataTable();

            var statusColumnIndex = $('.datatable thead th').filter(function () {
                return $(this).text().trim().toUpperCase() === 'STATUS';
            }).index();

            function getSelectedFilters() {
                return $('.filter_checkbox:checked').map(function () {
                    return $(this).siblings('label').text().trim().toUpperCase();
                }).get();
            }

            $.fn.dataTable.ext.search.push(function (settings, data, dataIndex) {
                if (statusColumnIndex === -1) return true;

                var selectedFilters = getSelectedFilters();
                var status = data[statusColumnIndex] ? data[statusColumnIndex].trim().toUpperCase() : "";

                return selectedFilters.length === 0 || selectedFilters.includes(status);
            });

            $('.filter_checkbox').on('change', function () {
                table.draw();
            });
        });

        document.addEventListener('input', function(e) {
            if (e.target.classList.contains('no-first-space')) {
                e.target.value = e.target.value.replace(/^\s/, '');
            }
        });
        $(document).ready(function () {
            $('.custom_hamburg').click(function () {
                $('#responsiveMenu').toggleClass('d-none');
            });
        });
    </script>




    <?php echo $__env->yieldPushContent('js'); ?>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/theme/layout/master.blade.php ENDPATH**/ ?>