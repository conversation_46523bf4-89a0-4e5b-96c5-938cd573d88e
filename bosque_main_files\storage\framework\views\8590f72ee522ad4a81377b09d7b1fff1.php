<!DOCTYPE html>
<html lang="en">
<head>
    <?php echo $__env->make('common_header_meta_links', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <style>
        .goog-te-banner-frame.skiptranslate,
        body > .skiptranslate {
            display: none !important;
        }
        body {
            top: 0 !important;
        }
        /* Prevent translation of language selector */
        #languageSwitcher,
        #languageSwitcher option,
        .notranslate {
            translate: no !important;
        }
        .goog-te-combo {
            display: none !important;
        }

    </style>
    <?php echo $__env->yieldPushContent('css'); ?>
    <script src="https://maps.googleapis.com/maps/api/js?key=<?php echo e(env('GOOGLE_MAP_API_KEY')); ?>&libraries=places"></script>
</head>
<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank loading translate">
<div class="" id="kt_app_root">
    <div class="preloader">
        <div class="cssload-speeding-wheel">
            <div class="loader_img">
                <img src="<?php echo e(asset('website')); ?>/assets/images/Loading/An.png">
            </div>
        </div>
    </div>
    <header>
        <?php if(!Request::is('login') && !Request::is('register')): ?>
            <nav class="navbar navbar-expand-lg ">
                <div class="container custom_container">
                    <div class="custom_nav">

                        <div class="resp_logo">
                            <a class="navbar-brand site_logo" href="<?php echo e(url(' ')); ?>">
                                <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/logo_black.png" class="logo-default" />
                            </a>
                            <div class="responsive_top_header">
                                <?php echo $__env->make('website.login_cart_navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                            </div>
                        </div>

                        <div class="product_search_togglers mega_menu_banner">
                            <div class="custom_product_search responsive_hidden">
                                <div class="product_search txt_field">
                                    <input type="text" id="product_search_responsive" placeholder="Find your product" class="form-control">
                                    <button class="upload_img_modal_btn" type="button" data-bs-toggle="modal" data-bs-target="#image_upload_modal"><i class="fa-solid fa-upload"></i></button>
                                    <i class="fa-solid fa-magnifying-glass search_icon"></i>
                                    <ul id="product_list_responsive" class="dropdown-menu product_list_dropdown"></ul>
                                </div>
                            </div>
                            <?php if(Route::currentRouteName() === 'index'): ?>
                                <div class="custom_mega_menu_list responsive_mega_menu">
                                    <div class="dropdown ">
                                        <button class=" dropdown-toggle show" type="button"  aria-expanded="false" data-bs-toggle="dropdown" >
                                            <i class="fa-solid fa-list"></i>  Browse All Categories <i class="fa-solid fa-chevron-down"></i>
                                        </button>
                                        <ul class="dropdown-menu">
                                            <?php $__currentLoopData = $activeCategories->where('parent_category_id',null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activeCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <li><a class="dropdown-item" href="<?php echo e(url('category',$activeCategory->unique)); ?>"><?php echo e($activeCategory->name); ?></a></li>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </ul>
                                    </div>
                                </div>
                                <button class="responsive_hidden browse_category_toggle" type="button" >
                                    <span class="navbar-toggler-icon"></span>
                                </button>
                            <?php endif; ?>
                            <div class="collapse navbar-collapse main_navbar custom_flex" id="navbarSupportedContent">
                                <a class="navbar-brand site_logo" href="<?php echo e(url(' ')); ?>">
                                    <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/logo_black.png" class="logo-default" />
                                </a>
                                <div class="custom_product_search">

                                    <div class="custom_dropdown custom_simple_select_dropdown">
                                        <div class="dropdown">
                                            <select id="all_categories" class="dropdown-select custom_icon_dropdown category_custom_select2" aria-label="All Categories">
                                                <option value=""><?php echo e(__('All Categories')); ?></option>
                                                <?php $__currentLoopData = $activeCategories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activeCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e(url('category', $activeCategory->unique)); ?>" <?php if(isset($selectedCategory) && $selectedCategory!=null && $selectedCategory->unique==$activeCategory->unique): ?> selected <?php endif; ?> ><?php echo e($activeCategory->name); ?></option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </div>
                                    </div>
                                    <div class="product_search txt_field">
                                        <input type="text" id="product_search" placeholder="Find your product" class="form-control">
                                        <button class="upload_img_modal_btn" type="button" data-bs-toggle="modal" data-bs-target="#image_upload_modal"><i class="fa-solid fa-upload"></i></button>
                                        <i class="fa-solid fa-magnifying-glass search_icon"></i>
                                        <ul id="product_list" class="dropdown-menu product_list_dropdown"></ul>
                                    </div>

                                </div>
                                <div class="header_icons">
                                    <div class="english_arabic_converter_wrap" style="">
                                        <select id="languageSwitcher" class="form-select notranslate" style="" translate="no">
                                            <option value="en" class="notranslate" translate="no">English</option>
                                            <option value="hi" class="notranslate" translate="no">Hindi</option>
                                        </select>
                                        <div class="global_lang_wrap" style="">
                                            <i class="fa-solid fa-globe"></i>
                                        </div>
                                        <div id="google_translate_element" style="position: absolute; top: 0; right: 0; z-index: 1000; opacity: 0; pointer-events: none;"></div>
                                    </div>

                                    <?php echo $__env->make('website.login_cart_navbar', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </nav>
        <?php endif; ?>
    </header>
    <?php if(Route::currentRouteName() === 'index'): ?>
        <section class="mega_menu_banner">
            <div class="container custom_container">
                <div class="row">
                    <div class="col-md-12">
                        <div class="custom_mega_menu_list">
                            <div class="dropdown ">
                                <button class=" dropdown-toggle show" type="button"  aria-expanded="false" data-bs-toggle="dropdown" >
                                    <i class="fa-solid fa-list"></i>  Browse All Categories <i class="fa-solid fa-chevron-down"></i>
                                </button>
                                <ul class="dropdown-menu ">
                                    <?php $__currentLoopData = $activeCategories->where('parent_category_id',null); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activeCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li><a class="dropdown-item" href="<?php echo e(url('category',$activeCategory->unique)); ?>"><?php echo e($activeCategory->name); ?></a></li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </ul>
                            </div>
                            <div class="mega_menu" >
                                <ul class="mega_list">
                                    <li class="nav_item">
                                        <a href="javascript:void(0);" class="nav_link" data-target="#product_sale_sec"><i class="fa-solid fa-fire"></i> Super Deals</a>
                                    </li>
                                    <?php $__currentLoopData = $activeCategories->where('parent_category_id', null)->take(4); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $activeCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <li class="nav_item super_deal_cat">
                                            <select class="accessories_select category_custom_select2" onchange="location = this.value;">
                                                <?php $__currentLoopData = $activeCategory->children->where('status', 1); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $childCategory): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                    <option value="<?php echo e(url('category', $childCategory->unique)); ?>">
                                                        <?php echo e($childCategory->name); ?>

                                                    </option>
                                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                            </select>
                                        </li>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    <li class="nav_item">
                                        <a class="nav_link more_view" href="<?php echo e(url('category', 'all')); ?>">More  <img  src="<?php echo e(asset('website')); ?>/assets/images/up_right.svg" /></a>
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
    <main>
        <?php echo $__env->yieldContent('content'); ?>
    </main>


        <footer class="footer">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <div class="custom_justify">
                        <h2>Stay connected with us</h2>
                        <a href="<?php echo e(url('category')); ?>" class="btn_main btn_pink">Start Exploring  <img src="<?php echo e(asset('website')); ?>/assets/images/chevron_up.svg" alt=""></a>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="footer_description">
                        <h6> <?php echo e($contentManagements->where('slug', 'about_us')->first()->title); ?></h6>
                        <a class="navbar-brand site_logo" href="#kt_body">
                            <img alt="Logo" src="<?php echo e(asset('website')); ?>/assets/images/logo_black.png" class="logo-default" />
                        </a>
                        <?php echo $contentManagements->where('slug', 'about_us')->first()->description; ?>

                        <div class="follow_us custom_flex">
                            <a href="<?php echo e($contentManagements->where('slug', 'facebook')->first()->description); ?>" target="_blank" class="icons_div">
                                <i class="fa-brands fa-facebook-f"></i>
                            </a>
                            <a href="<?php echo e($contentManagements->where('slug', 'facebook')->first()->description); ?>" target="_blank"  class="icons_div">
                                <i class="fa-brands fa-instagram"></i>
                            </a>
                            <a href="<?php echo e($contentManagements->where('slug', 'facebook')->first()->description); ?>" target="_blank"  class="icons_div">
                                <i class="fa-brands fa-pinterest-p"></i>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="col-md-2">
                    <h6>Shopping with us</h6>
                    <ul>
                        <li><a href="<?php echo e(url('make-payment')); ?>">Make Payment</a></li>
                        <li><a href="<?php echo e(url('delivery-options')); ?>">Delivery Options</a></li>
                        <li><a href="<?php echo e(url('buyer-protection')); ?>">Buyer Protection</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6>Help</h6>
                    <ul>
                        <li><a href="<?php echo e(url('customer-support')); ?>">Customer Support</a></li>
                        <li><a href="<?php echo e(url('delivery-details')); ?>">Delivery Details</a></li>
                        <li><a href="<?php echo e(url('terms-conditions')); ?>">Terms & Conditions</a></li>
                        <li><a href="<?php echo e(url('privacy-policy')); ?>">Privacy Policy</a></li>
                    </ul>
                </div>
                <div class="col-md-2">
                    <h6>Pay With</h6>
                    <div class="payment_methods">
                        <a  href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/Visa.png" alt="">
                            </div>
                        </a>
                        <a  href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/discover.png" alt="">
                            </div>
                        </a>
                        <a href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/Mastercard.png" alt="">
                            </div>
                        </a>
                        <a href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/visa_debit.png" alt="">
                            </div>
                        </a>
                        <a href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/express.png" alt="">
                            </div>
                        </a>
                        <a href="javascript:void(0)" class="payment_images">
                            <div>
                                <img src="<?php echo e(asset('website')); ?>/assets/images/apply_pay.png" alt="">
                            </div>
                        </a>
                    </div>
                </div>
            </div>
        </div>

    </footer>


    <div id="chatbot-container" class="chatbot_container">
        <div id="chatbot-header" class="chatbot-header">
            <button id="close-chat" class="close-chat-btn"><i class="fa-solid fa-angle-down"></i></button>
        </div>
        <div id="chatbot-body" class="chatbot-body">
            <div class="cartoon-rotation">
                <img class="cartoon-rotation-img" src="<?php echo e(asset('website')); ?>/assets/images/Scrolling/0.png" alt="Static Image">
            </div>
            <div id="messages" class="messages"> </div>
        </div>
        <div id="chatbot-footer" class="chatbot-footer">
            <div class="upload_message_wrap_img_sec">
                <div class="upload_message_wrap_img">
                    <img src="" id="image_preview" class="image_preview" alt="Image Preview" style="display: none;" />
                </div>
            </div>
            <div class="upload_img_btn_chatbot">
                <input type="file" class="file-input" name="" accept="image/*" required />
                <a class="upload_img_chatbot_img_icon" href="javascript:void(0)">
                    <img src="<?php echo e(asset('website')); ?>/assets/images/chatbot_upload_icon.png">
                </a>
            </div>
            <i class="fa-solid fa-magnifying-glass search_icon_chatbot"></i>
            <input type="text" id="user-input" class="user-input" placeholder="Ask Ai or Search" />
            <button id="send-btn" class="send-btn"><img src="<?php echo e(asset('website')); ?>/assets/images/chatbot_whatsap_enter.png"></button>
        </div>
        <div class="chat-loader" style="display:none;">
            <span></span><span></span><span></span><span></span>
        </div>
        <div class="resize-handle resize-top"></div>
        <div class="resize-handle resize-left"></div>
        <div class="resize-handle resize-top-left"></div>
        <div class="resize-handle resize-top-right"></div>
    </div>


    <div class="offcanvas offcanvas-end" tabindex="-1" id="ProductCartModal" aria-labelledby="offcanvasRightLabel"></div>
    <div class="modal fade upload_search_image"  id="image_upload_modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5>Upload Image to Search Product</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form method="post" action="<?php echo e(url('search-product')); ?>" enctype="multipart/form-data" id="uploadImageForm">
                        <?php echo csrf_field(); ?>
                        <div class="row">
                            <div class="col-md-12">
                                <div id="custom_file_upload" class="custom_file_upload">
                                    <div class="append_type_wrapper">
                                        <div class="append_type_file">
                                            <input type="file" class="file-input" name="image" accept="image/*" required />
                                            <a class="upload_img_remove" href="javascript:void(0)">
                                                <i class="fa-solid fa-image"></i>
                                            </a>
                                            <img src="" class="image_preview" alt="Image Preview" style="display: none;" />
                                            <button type="button" class="close-btn append_img_div_remove" style="display:none;">
                                                <i class="fa-solid fa-close"></i>
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-12">
                                <div class="modal_footer">
                                    <button type="button" class="btn_main btn_orange" data-bs-dismiss="modal">Close</button>
                                    <button type="submit" class="btn_main btn_maroon">Search Product</button>
                                </div>
                            </div>
                        </div>


                    </form>
                </div>

            </div>
        </div>
    </div>

    <div class="modal cart_modal" id="show_product_detail_modal" tabindex="-1" aria-labelledby="exampleModalLabel" aria-hidden="true" data-bs-backdrop="static" data-bs-keyboard="false">
    </div>
</div>
<?php echo $__env->make('common_script_code_for_website_section', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<?php echo $__env->make('website.includes.backend_helper_script_functions', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<script src="<?php echo e(asset('website/assets/js/frontend_helper_functions.js')); ?>"></script>
<script type="text/javascript">
    function googleTranslateElementInit() {
        try {
            new google.translate.TranslateElement({
                pageLanguage: 'en',
                includedLanguages: 'en,hi',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false,
                multilanguagePage: true
            }, 'google_translate_element');

            // Wait for the widget to be ready
            setTimeout(function() {
                initializeLanguageSwitcher();
            }, 1000);
        } catch (error) {
            console.error('Google Translate initialization failed:', error);
        }
    }
</script>
<script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

<script>
    function changeLanguage(lang) {
        try {
            // Set the Google Translate cookie with proper domain and path
            const domain = window.location.hostname;
            document.cookie = `googtrans=/en/${lang}; path=/; domain=${domain}; max-age=31536000`;

            // Also try the alternative cookie format
            document.cookie = `googtrans=/en/${lang}; path=/; max-age=31536000`;

            // Trigger Google Translate programmatically
            const translateElement = document.querySelector('.goog-te-combo');
            if (translateElement) {
                translateElement.value = lang;
                translateElement.dispatchEvent(new Event('change'));
            } else {
                // Fallback: reload the page
                setTimeout(() => {
                    window.location.reload();
                }, 100);
            }
        } catch (error) {
            console.error('Language change failed:', error);
            window.location.reload();
        }
    }

    function initializeLanguageSwitcher() {
        try {
            // Store original option texts to prevent translation
            const languageSwitcher = document.getElementById('languageSwitcher');
            if (languageSwitcher) {
                // Store original texts
                const originalTexts = {};
                languageSwitcher.querySelectorAll('option').forEach(option => {
                    originalTexts[option.value] = option.textContent;
                });

                // Restore original texts periodically to prevent translation
                setInterval(() => {
                    languageSwitcher.querySelectorAll('option').forEach(option => {
                        if (originalTexts[option.value] && option.textContent !== originalTexts[option.value]) {
                            option.textContent = originalTexts[option.value];
                        }
                    });
                }, 500);
            }

            // Check for existing translation cookie
            const cookieLang = getCookieLanguage();

            if (cookieLang && languageSwitcher) {
                languageSwitcher.value = cookieLang;
                updateSelectOptions(cookieLang);
            }

            // Add event listener for language switcher
            if (languageSwitcher) {
                languageSwitcher.addEventListener('change', (e) => {
                    changeLanguage(e.target.value);
                });
            }
        } catch (error) {
            console.error('Language switcher initialization failed:', error);
        }
    }

    function getCookieLanguage() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'googtrans') {
                const match = value.match(/\/en\/([^\/]+)/);
                return match ? match[1] : null;
            }
        }
        return null;
    }

    function updateSelectOptions(selectedLang) {
        $('#languageSwitcher option').each(function() {
            if ($(this).val() === selectedLang) {
                $(this).attr('selected', true);
            } else {
                $(this).removeAttr('selected');
            }
        });
    }

    $(document).ready(function() {
        $('.nav_link').on('click', function(e) {
            if ($(this).hasClass('more_view')) {
                return; // Allow the link to navigate normally
            }
            e.preventDefault();
            var target = $(this).data('target');
            var $targetElement = $(target);

            if ($targetElement.length) {
                $('html, body').animate({
                    scrollTop: $targetElement.offset().top
                }, 600);
            }
        });
    });

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        // Wait a bit for Google Translate to load
        setTimeout(initializeLanguageSwitcher, 2000);
    });

    $(function() {
        $('a[href^="#"]').click(function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $($(this).attr('href')).offset().top
            }, 1500);
        });
    });
</script>
<script>


    $(document).on('click','.storage-option',function (){
        var variant_image = $(this).data('variant-image');
        $('.swiper-slide.swiper-slide-active .main_slider_product_img_wrapper').find('img').attr('src', variant_image);
    });



</script>
<?php echo $__env->yieldPushContent('js'); ?>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/website/layout/master.blade.php ENDPATH**/ ?>