<script>
    $(document).ready(function () {
        $('.show_cart_items_modal').on('click', function () {
            $.ajax({
                url     : '<?php echo e(url('show-cart-items')); ?>',
                type    : 'GET',
                success : function (response) {
                    if (response.type == 'error') {
                        swal.fire({
                            'title': response.title,
                            'text' : response.message,
                            'icon' : response.type,
                            'timer': 5000,
                        });
                    }
                    if (response.type == 'success') {
                        $('#ProductCartModal').html(response.view);
                        var offcanvas = new bootstrap.Offcanvas(document.getElementById('ProductCartModal'));
                        offcanvas.show();
                    }
                }
            });
        });
    });
    $(document).on('click', '.minus_icon', function() {
        var $button = $(this);
        var inputField = $button.siblings('.product_order_num');
        var cartItemId = $button.data('id');
        var currentValue = parseInt(inputField.val());
        var $priceElement = $('.single_product_total_price_' + cartItemId);
        $button.prop('disabled', true);
        if ($('#shipping_cost_label').length) {
            var shippingCost = $('#shipping_cost_label').text();
        }
        if (currentValue > 1) {
            $.ajax({
                url: '<?php echo e(url('cart-item-quantity')); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    id: cartItemId,
                    quantity: currentValue - 1
                },
                success: function(response) {
                    $button.prop('disabled', false);
                    if (response.type === 'error'){
                        alert(response.message)
                    }else {
                        inputField.val(currentValue - 1);
                        $priceElement.text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + response.updatedPrice);
                        $('.all_cart_products_total_items').text('(' + response.cartCount + ' items)');
                        $('.get_cart_items_count').text(response.cartCount);
                        $('.all_cart_products_total_price').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + response.totalPrice);
                        if ($('#shipping_cost_label').length) {
                            var cleanedShippingCost = parseFloat(shippingCost.replace('£', '').trim());
                            $('#shipping_cost_label').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + cleanedShippingCost.toFixed(2)); // Update the label with cleaned value
                            $('.all_cart_products_total_price').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + (parseFloat(response.totalPrice) + cleanedShippingCost).toFixed(2));
                        }

                    }
                },
                error: function(response) {
                    $button.prop('disabled', false);
                    alert(response.responseJSON.message);
                }
            });
        }
    });
    $(document).on('click', '.plus_icon', function() {
        var $button = $(this);
        var inputField = $button.siblings('.product_order_num');
        var cartItemId = $button.data('id');
        var currentValue = parseInt(inputField.val());
        var $priceElement = $('.single_product_total_price_' + cartItemId);
        $button.prop('disabled', true);
        if ($('#shipping_cost_label').length) {
            var shippingCost = $('#shipping_cost_label').text();
        }
        $.ajax({
            url: '<?php echo e(url('cart-item-quantity')); ?>',
            type: 'POST',
            data: {
                _token  : '<?php echo e(csrf_token()); ?>',
                id      : cartItemId,
                quantity: currentValue + 1
            },
            success: function(response) {
                $button.prop('disabled', false);
                if (response.type === 'error'){
                    alert(response.message)
                }else {
                    inputField.val(currentValue + 1);
                    $priceElement.text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + response.updatedPrice);
                    $('.all_cart_products_total_items').text('(' + response.cartCount + ' items)');
                    $('.get_cart_items_count').text(response.cartCount);
                    $('.all_cart_products_total_price').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + response.totalPrice);
                    if ($('#shipping_cost_label').length) {
                        var cleanedShippingCost = parseFloat(shippingCost.replace('£', '').trim());
                        $('#shipping_cost_label').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + cleanedShippingCost.toFixed(2)); // Update the label with cleaned value
                        $('.all_cart_products_total_price').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + (parseFloat(response.totalPrice) + cleanedShippingCost).toFixed(2));
                    }


                }
            },
            error: function(response) {
                $button.prop('disabled', false);
                alert(response.responseJSON.message);
            }
        });
    });
    $(document).on('click','.delete_cart_item', function () {
        var id = $(this).data('id');
        Swal.fire({
            title: 'Are you sure?',
            text: 'You are about to remove this item from your cart.',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonText: 'Yes, remove it!',
            cancelButtonText: 'No, keep it',
        }).then((result) => {
            if (result.isConfirmed) {
                $.ajax({
            url     : '<?php echo e(route('delete-cart-item')); ?>/'+ id,
            type    : 'GET',
            success : function (response) {
                swal.fire({
                    'title': response.title,
                    'text' : response.message,
                    'icon' : response.type,
                    'timer': 5000,
                });
                $('.all_cart_products_total_items').text('(' + response.cartCount + ' items)');
                $('.get_cart_items_count').text(response.cartCount);
                $('.all_cart_products_total_price').text("<?php echo e($ACTIVE_CURRENCY_SYMBOL); ?>" + response.totalPrice);

                $('.product_add_to_cart_single_wrapper[data-id="' + id + '"]').remove();
            }

        });
            }
        });
    });

</script>
<script>
    $(document).ready(function () {
$('.chatbot_container').hide();
        $("#product_search").on("keyup", function () {
            let query = $(this).val().trim();
            const imageElement = document.querySelector(".cartoon-rotation-img");
            if (query.length > 0) {
                imageElement.src = `<?php echo e(asset('website')); ?>/assets/images/Searching/An.png`;
                $.ajax({
                    url: "<?php echo e(route('search.products')); ?>",
                    type: "GET",
                    data: { query: query },
                    success: function (data) {
                        let dropdown = $("#product_list");
                        dropdown.empty();
                        if (data.length > 0) {
                            data.forEach(function (product) {
                                dropdown.append(`<li class="dropdown-item" data-id="${product.id}">${product.name}</li>`);
                            });
                            dropdown.show();
                        } else {
                            dropdown.hide();
                        }
                    },
                    error: function(xhr, status, error) {
                    }
                });
            } else {
                imageElement.src = `<?php echo e(asset('website')); ?>/assets/images/Scrolling/1.png`; // 👈 Reset image
                $("#product_list").hide();
            }
        });
        $(document).on("click", "#product_list li", function () {
            let selectedProduct = $(this).text();
            let productId = $(this).attr("data-id");
            $("#product_search").val(selectedProduct);
            $("#product_list").hide();
            window.location.href = "<?php echo e(url('')); ?>/product/" + productId;
        });
        $(document).click(function (e) {
            if (!$(e.target).closest(".product_search").length) {
                $("#product_list").hide();
            }
        });
        $('.category_custom_select2').select2({dropdownCssClass : 'select2_custom_class'});

        $("#send-btn").on("click", function() {
            sendMessage();
        });
        $("#user-input").on("keypress", function(e) {
            if (e.which == 13 && !e.shiftKey) {
                e.preventDefault();
                sendMessage();
            }
        });
        function sendMessage() {
            var userMessage = $("#user-input").val();
            var imageData = $("#image_preview").attr("src");
            var botImage = $(".file-input")[0].files[0];
            if (userMessage.trim() !== "" || (imageData && imageData !== "")) {
                if (userMessage.trim() !== "") {
                    displayMessage("user", userMessage);
                    $("#user-input").val(""); // Clear input
                }
                if (imageData && imageData !== "") {
                    displayImage("user", imageData);
                    $("#image_preview").attr("src", "").hide();
                    $(".upload_message_wrap_img_sec").hide();
                    $(".remove_image").remove();
                }
                var loader = $(".chat-loader");
                $("#messages").append(loader);
                loader.show();
                var formData = new FormData();
                if (botImage) {
                    formData.append("image", botImage);
                }
                if (userMessage.trim() !== "") {
                    formData.append("message", userMessage);
                }
                formData.append("_token", "<?php echo e(csrf_token()); ?>");
                formData.append("timestamp", "<?php echo e(time()); ?>");
                formData.append("user_id", "<?php echo e(auth()->user() ? auth()->user()->id : null); ?>");
                formData.append("user_ip", "<?php echo e(request()->ip()); ?>");
                $.ajax({
                    url: "<?php echo e(route('chat.sendMessage')); ?>",
                    method: "POST",
                    data: formData,
                    processData: false,
                    contentType: false,
                    success: function(response) {
                        loader.hide();
                        var botMessage = response.response;
                        displayMessage("bot", botMessage);
                        getProductsByCategoryAndProductId(null,response.result);
                        $('#searched-items-tab-li').show();
                        $('#all-tab').removeClass('active');
                        setTimeout(function() {
                            if ($('#messages').children().length > 0) {
                                if (!$('.cartoon-rotation').hasClass('chat_start')) {
                                    $('.cartoon-rotation').addClass('chat_start');
                                }
                            } else {
                                $('.cartoon-rotation').removeClass('chat_start');
                            }
                        }, 500);

                    },
                    error: function() {
                        loader.hide();
                        displayMessage("bot", "Sorry, there was an error.");
                    }
                });
            }
        }


        $(".close-chat-btn").on("click", function() {
            $(".chatbot_container").fadeOut(300);
            $(".inner_main_wrap").animate({ width: "100%" }, 300);
            $(".chatbot_icon_toggle").fadeIn(300);
        });


        $(".sparkle-button").on("click", function() {
            $(".chatbot_container").fadeToggle(300, function() {
                $(".cartoon-rotation").removeClass("chat_start");
                const imageElement = document.querySelector(".cartoon-rotation-img");
                imageElement.src = `<?php echo e(asset('website')); ?>/assets/images/Welcoming/An.png`;
                $(".inner_main_wrap").animate({ width: "75%" }, 300);
                $(".chatbot_icon_toggle").fadeOut(300);
                setTimeout(function() {
                    imageElement.src = `<?php echo e(asset('website')); ?>/assets/images/Scrolling/1.png`;
                    setTimeout(function() {
                        $(".cartoon-rotation").addClass("chat_start");
                        imageElement.src = `<?php echo e(asset('website')); ?>/assets/images/Scrolling/1.png`;
                    }, 10000);
                }, 1000);
            });
            $(".chatbot_icon_toggle").fadeOut(300);
        });

    });



    $('#all_categories').change(function () {
        if($(this).val()===''){
            window.location.href = "<?php echo e(url('category')); ?>";
        }else{
            window.location.href = $(this).val();
        }
    });

    $(document).on("click", ".upload_img_chatbot_img_icon", function () {
        $(this).closest(".upload_img_btn_chatbot").find("input[type='file']").click();
    });
    $(document).on("change", ".chatbot_container .upload_img_btn_chatbot input[type='file']", function () {
        const file = this.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                const previewImg = $(".upload_message_wrap_img .image_preview");
                previewImg.attr('src', e.target.result).show();
                $(".upload_message_wrap_img_sec").show();
                $(".upload_message_wrap_img .remove_image").remove();
                $(".upload_message_wrap_img").append(`<span class="remove_image" style=" position: absolute; top: 0; right: 0; background: #C12A6D; color: #fff; border-radius: 50%; width: 20px; height: 20px; text-align: center; line-height: 20px; cursor: pointer; font-size: 14px; ">×</span>`);
                $(".upload_message_wrap_img").css("position", "relative");
            };
            reader.readAsDataURL(file);
        }
    });
    $(document).on("click", ".remove_image", function () {
        const container = $(this).closest(".upload_message_wrap_img");
        container.find(".image_preview").attr('src', '').hide();
        $(this).remove();
        $(".upload_img_btn_chatbot input[type='file']").val("");
        $(".upload_message_wrap_img_sec").hide();
    });


    function displayMessage(sender, message) {
        var messageContainer = $("<div>").addClass(sender + "-message").text(message);
        $("#messages").append(messageContainer);
        $("#messages").scrollTop($("#messages")[0].scrollHeight);
    }
    function displayImage(sender, imageUrl) {
        var imgContainer = $("<div>").addClass(sender + "-message");
        var imgTag = $("<img>").attr("src", imageUrl).css({
            maxWidth: "200px",
            borderRadius: "5px",
            display: "block"
        });
        imgContainer.append(imgTag);
        $("#messages").append(imgContainer);
        $("#messages").scrollTop($("#messages")[0].scrollHeight);
    }

    function getProductsByCategoryAndProductId(category_id=null,chat_id=null){
        $.ajax({
            url: '<?php echo e(url('fetch-product-by-category')); ?>/' + category_id+'/'+chat_id,
            type: 'GET',
            success: function(response) {
                $('#product-container').html(response);
                $('#loader').hide();
            },
            error: function() {
                $('#loader').hide();
                $('#searched-items-tab-li').show();
                alert('Failed to fetch products. Please try again later.');
            }
        });
    }

    var currentRequest = false;
    function showProductDetailModalOrAddToCart(product_id,element) {
        var quantity = $(element).closest('.add_to_crt_counter_plus').find('.counter_product_wrap_input').val();
        if(currentRequest){
            return false;
        }else{
            currentRequest = true;
        }
        if(quantity!=undefined){
            quantity = quantity;
        }else{
            quantity = 1;
        }
        $.ajax({
            url: "<?php echo e(url('product-detail-modal')); ?>/" + product_id,
            type: 'GET',
            success: function(response) {
                $('#show_product_detail_modal').html(response);
                $('#quantity_counter').attr('value',quantity);

                $('#show_product_detail_modal').modal('show');
                initializeSwiper();
            },
            error: function() {
                alert('Failed to fetch product details. Please try again later.');
            },
            complete:function (){
                currentRequest = false;
            }
        });
    }

    function initializeSwiper() {
        var swiper = new Swiper(".single_pro_color_swiper", {
            slidesPerView: 5,
            spaceBetween: 2,
            navigation: {
                nextEl: ".swiper-button-next",
                prevEl: ".swiper-button-prev",
            },
        });
    }

    $(document).ready(function() {

        $(document).on('change', '#pricing-plan-switch' , function() {
            if ($(this).prop('checked')) {
                $('.guest_login').hide();
                $('.existing_user').show();
            } else {
                $('.guest_login').show();
                $('.existing_user').hide();
            }
        });
    });

    $(document).on('click', '.guest-checkout-button', function () {
        var email = $('#existing-user-email').val().trim();
        var password = $('#existing_user_password').val().trim();

        $('#existing-user-email').removeClass('is-invalid').attr('placeholder', 'Enter email for existing user login');
        $('#existing_user_password').removeClass('is-invalid').attr('placeholder', 'Enter Password');
        $('.txt_field1').removeClass('error');

        $('#existing-user-email').siblings('.error-message').remove();
        $('#existing_user_password').siblings('.error-message').remove();

        $('.guest-checkout-button').prop('disabled', true).text('Logging in...');

        if (email && password) {
            $.ajax({
                url: '<?php echo e(url('guest-login')); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    email: email,
                    password: password,
                    login_type: 'existing_user'
                },
                success: function(response) {
                    $('.error-message').remove();
                    $('#existing-user-email, #existing_user_password').removeClass('is-invalid');

                    if (response.type === 'success') {
                        $('.add_to_cart_form').trigger('submit');
                        $('.header_icons').html(response.navbar_view);
                    } else {
                        if (response.message) {
                            if (response.errorType == 'email') {
                                $('#existing-user-email').addClass('is-invalid').after('<div class="error-message" style="color: red;">' + response.message + '</div>');
                            } else if (response.errorType == 'password') {
                                $('#existing_user_password').addClass('is-invalid').after('<div class="error-message" style="color: red;">' + response.message + '</div>');
                            } else {
                                alert(response.message);
                            }
                        }
                        $('.guest-checkout-button').prop('disabled', false).text('Login');
                    }
                },
                error: function(xhr) {
                    $('.error-message').remove();
                    $('#existing-user-email, #existing_user_password').removeClass('is-invalid');

                    if (xhr.responseJSON && xhr.responseJSON.errors) {
                        const errors = xhr.responseJSON.errors;

                        if (errors.email) {
                            $('#existing-user-email').addClass('is-invalid').after('<div class="error-message" style="color: red;">' + errors.email[0] + '</div>');
                        }
                        if (errors.password) {
                            $('#existing_user_password').addClass('is-invalid').after('<div class="error-message" style="color: red;">' + errors.password[0] + '</div>');
                        }
                    } else {
                        alert("Something went wrong. Please try again.");
                    }

                    $('.guest-checkout-button').prop('disabled', false).text('Login');
                }
            });
        } else {
            $('.error-message').remove();
            $('#existing-user-email, #existing_user_password').removeClass('is-invalid');

            if (!email) {
                $('#existing-user-email').addClass('is-invalid').attr('placeholder', 'Please enter your email.').after('<div class="error-message" style="color: red;">Email is required.</div>');
            }
            if (!password) {
                $('#existing_user_password').addClass('is-invalid').attr('placeholder', 'Please enter your password.').after('<div class="error-message" style="color: red;">Password is required.</div>');
            }

            $('.guest-checkout-button').prop('disabled', false).text('Login');
        }

    });

    $(document).on('click', '.guest-checkout-button', function () {
        var guestEmail = $('#guest-checkout-email').val();
        var emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/; // Basic email validation regex

        if (guestEmail == '') {
            // Remove previous error messages for this input
            $('#guest-checkout-email').siblings('.error-message').remove();

            $('#guest-checkout-email').focus().addClass('is-invalid');
            // Show error message *only* when invalid
            $('#guest-checkout-email').after('<div class="error-message" style="color: red;">Email is required.</div>');

            return false;
        } else if (!emailPattern.test(guestEmail)) {
            $('#guest-checkout-email').siblings('.error-message').remove();

            $('#guest-checkout-email').focus().addClass('is-invalid');
            $('#guest-checkout-email').after('<div class="error-message" style="color: red;">Please enter a valid email address.</div>');

            return false;
        } else {
            // Email is valid, so remove error class and any error message
            $('#guest-checkout-email').removeClass('is-invalid');
            $('#guest-checkout-email').siblings('.error-message').remove();

        $.ajax({
                url: '<?php echo e(url('guest-login')); ?>',
                type: 'POST',
                data: {
                    _token: '<?php echo e(csrf_token()); ?>',
                    email: guestEmail,
                    'login_type':'guest_user'
                },
                success: function (response) {
                    if (response.type == 'success') {
                        $('.header_icons').html(response.navbar_view)
                        $('.add_to_cart_form').trigger('submit');
                    } else {
                        Swal.fire({
                            title: response.title,
                            text: response.message,
                            icon: response.type,
                        });
                    }
                },
                error: function(xhr) {
                    $('.error-message').remove();
                    $('.is-invalid').removeClass('is-invalid');

                    if(xhr.status === 422 && xhr.responseJSON.errors) {
                        let errors = xhr.responseJSON.errors;

                        if(errors.email) {
                            $('#guest-checkout-email').addClass('is-invalid').after(
                                `<div class="error-message" style="color:red; font-size:0.9em;">${errors.email[0]}</div>`
                            );
                        }
                    } else {
                        alert('Something went wrong.');
                    }
                }
            });
        }
    });

    $(document).on('click', '.close-swal-button', function() {
        Swal.close();
        <?php if(!Request::is('product/*')): ?>
        $('#show_product_detail_modal').modal('show');
        <?php endif; ?>

    });

</script>
<?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/website/includes/backend_helper_script_functions.blade.php ENDPATH**/ ?>