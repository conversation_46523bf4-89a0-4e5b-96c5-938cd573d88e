<!DOCTYPE html>
<html lang="en">
<head>
    <?php echo $__env->make('common_header_meta_links', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
    <style>
        .password-toggle { position: absolute; right: 10px; top: 50%; transform: translateY(-50%); background: none; border: none; padding: 0; margin: 0; cursor: pointer; }
        .login_input_wrapper { position: relative; }
        .text-danger { color: #dc3545 !important; font-size: 0.875em; display: block; margin-top: 0.25rem; }
        .form-text { font-size: 0.875em; margin-top: 0.25rem;}
        /* Clean up Google Translate UI */
        .goog-te-banner-frame.skiptranslate,
        body > .skiptranslate {
            display: none !important;
        }
        body {
            top: 0 !important;
        }
        /* Prevent translation of language selector */
        #languageSwitcher,
        #languageSwitcher option,
        .notranslate {
            translate: no !important;
        }
        .goog-te-combo {
            display: none !important;
        }

    </style>
    <?php echo $__env->yieldPushContent('css'); ?>

</head>
<body id="kt_body" data-bs-spy="scroll" data-bs-target="#kt_landing_menu" class="bg-body position-relative app-blank login_master_body translate">
<div class="" id="kt_app_root">
    <div class="preloader">
        <div class="cssload-speeding-wheel">
            <div class="loader_img">
                <img src="<?php echo e(asset('website')); ?>/assets/images/dashboard_logo.png">
            </div>
            <div class="loading_icon">
                <span><i class="fa-solid fa-circle"></i> </span>
                <span><i class="fa-solid fa-circle"></i> </span>
                <span><i class="fa-solid fa-circle"></i> </span>
            </div>
        </div>
    </div>
    <header>
           <nav class="navbar  login_navbar_header">
                <div class="container-fluid">
                    <div class="custom_nav">
                        <div class="login_logo_wrapper">
                            <div>
                                <a href="<?php echo e(route('index')); ?>">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/login_icon_white_up.svg">
                                </a>
                            </div>
                        </div>
                        <div class="english_arabic_converter_wrap" style="">
                            <select id="languageSwitcher" class="form-select notranslate" style="" translate="no">
                                <option value="en" class="notranslate" translate="no">English</option>
                                <option value="hi" class="notranslate" translate="no">Hindi</option>
                            </select>
                            <div class="global_lang_wrap" style="">
                                <i class="fa-solid fa-globe"></i>
                            </div>
                            <div id="google_translate_element" style="position: absolute; top: 0; right: 0; z-index: 1000; opacity: 0; pointer-events: none;"></div>
                        </div>
                    </div>
                </div>
           </nav>
    </header>
    <?php echo $__env->yieldContent('content'); ?>
</div>
<footer class=" login_footer">
    <div class="container custom_container">
        <div class="row">
            <div class="col-md-12">
                <h6>This website is owned and operated by BOSQUE LTD (Company No. 16078294). © 2025 Company. All Rights Reserved</h6>
            </div>
        </div>
    </div>
</footer>
<?php echo $__env->make('common_script_code_for_website_section', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?>
<script src="<?php echo e(asset('website/assets/js/frontend_helper_functions.js')); ?>"></script>
<script type="text/javascript">
    function googleTranslateElementInit() {
        try {
            new google.translate.TranslateElement({
                pageLanguage: 'en',
                includedLanguages: 'en,hi',
                layout: google.translate.TranslateElement.InlineLayout.SIMPLE,
                autoDisplay: false,
                multilanguagePage: true
            }, 'google_translate_element');

            // Wait for the widget to be ready
            setTimeout(function() {
                initializeLanguageSwitcher();
            }, 1000);
        } catch (error) {
            console.error('Google Translate initialization failed:', error);
        }
    }
</script>
<script src="https://translate.google.com/translate_a/element.js?cb=googleTranslateElementInit"></script>

<script>
    function changeLanguage(lang) {
        try {
            // Set the Google Translate cookie with proper domain and path
            const domain = window.location.hostname;
            document.cookie = `googtrans=/en/${lang}; path=/; domain=${domain}; max-age=31536000`;

            // Also try the alternative cookie format
            document.cookie = `googtrans=/en/${lang}; path=/; max-age=31536000`;

            // Trigger Google Translate programmatically
            const translateElement = document.querySelector('.goog-te-combo');
            if (translateElement) {
                translateElement.value = lang;
                translateElement.dispatchEvent(new Event('change'));
            } else {
                // Fallback: reload the page
                setTimeout(() => {
                    window.location.reload();
                }, 100);
            }
        } catch (error) {
            console.error('Language change failed:', error);
            window.location.reload();
        }
    }

    function initializeLanguageSwitcher() {
        try {
            // Store original option texts to prevent translation
            const languageSwitcher = document.getElementById('languageSwitcher');
            if (languageSwitcher) {
                // Store original texts
                const originalTexts = {};
                languageSwitcher.querySelectorAll('option').forEach(option => {
                    originalTexts[option.value] = option.textContent;
                });

                // Restore original texts periodically to prevent translation
                setInterval(() => {
                    languageSwitcher.querySelectorAll('option').forEach(option => {
                        if (originalTexts[option.value] && option.textContent !== originalTexts[option.value]) {
                            option.textContent = originalTexts[option.value];
                        }
                    });
                }, 500);
            }

            // Check for existing translation cookie
            const cookieLang = getCookieLanguage();

            if (cookieLang && languageSwitcher) {
                languageSwitcher.value = cookieLang;
                updateSelectOptions(cookieLang);
            }

            // Add event listener for language switcher
            if (languageSwitcher) {
                languageSwitcher.addEventListener('change', (e) => {
                    changeLanguage(e.target.value);
                });
            }
        } catch (error) {
            console.error('Language switcher initialization failed:', error);
        }
    }

    function getCookieLanguage() {
        const cookies = document.cookie.split(';');
        for (let cookie of cookies) {
            const [name, value] = cookie.trim().split('=');
            if (name === 'googtrans') {
                const match = value.match(/\/en\/([^\/]+)/);
                return match ? match[1] : null;
            }
        }
        return null;
    }

    function updateSelectOptions(selectedLang) {
        $('#languageSwitcher option').each(function() {
            if ($(this).val() === selectedLang) {
                $(this).attr('selected', true);
            } else {
                $(this).removeAttr('selected');
            }
        });
    }

    $(document).ready(function() {
        $('.nav_link').on('click', function(e) {
            e.preventDefault();
            var target = $(this).data('target');
            var $targetElement = $(target);

            if ($targetElement.length) {
                $('html, body').animate({
                    scrollTop: $targetElement.offset().top
                }, 600);
            }
        });
    });

    // Initialize when DOM is ready
    document.addEventListener('DOMContentLoaded', () => {
        // Wait a bit for Google Translate to load
        setTimeout(initializeLanguageSwitcher, 2000);
    });

    $(function() {
        $('a[href^="#"]').click(function(e) {
            e.preventDefault();
            $('html, body').animate({
                scrollTop: $($(this).attr('href')).offset().top
            }, 1500);
        });
    });
</script>


<script>
    $(window).on('load', function() {
        setTimeout(function() {
            $('.preloader').css('visibility', 'hidden');
            $('#kt_app_body').css('display', 'inline');
        }, 200);
    });
</script>



<?php echo $__env->yieldPushContent('js'); ?>
</body>
</html>
<?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/layouts/app.blade.php ENDPATH**/ ?>