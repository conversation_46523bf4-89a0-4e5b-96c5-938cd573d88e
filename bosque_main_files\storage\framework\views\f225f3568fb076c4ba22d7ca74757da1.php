<?php $__env->startPush('css'); ?>
    <style>
        .attribute-row { display: flex; align-items: end; gap: 10px; margin-bottom: 10px; }
        .attribute-row .form-group { flex: 1; margin-bottom: 0; }
        .attribute-row .btn { flex-shrink: 0; height: 38px;padding: 0 10px; }
        .variant-row { display: flex; align-items: end; gap: 10px; margin-bottom: 10px; }
        .variant-row .form-group { flex: 1; margin-bottom: 0; }
        .variant-row .btn { flex-shrink: 0; height: 38px;padding: 0 10px; }
        .custom_colors{width: 100%;height:41px}
        .create_new_product .btn>i{padding:0}
        .custom_button{height:50px !important;}
        .dz-set-primary { position: absolute; top: 5px; left: 5px; background: rgba(0, 0, 0, 0.6); padding: 5px 7px; border-radius: 50%; cursor: pointer; transition: background 0.3s ease; }
        span.dz-set-primary {width:22px;height:22px;display:flex;align-items:center;justify-content:center}span.dz-set-primary i{font-size:11px}
        .dz-set-primary i { font-size: 16px; color: white; }
        .dz-set-primary.selected { background: #C12A6D; }
        .dz-set-primary.selected i{color:white;}
        .dz-remove { position: absolute; top: 5px;right: 5px; background: rgba(255, 0, 0, 0.8);padding: 5px 7px;border-radius: 50%;cursor: pointer;transition: background 0.3s ease;}
        .dz-remove i {font-size: 16px;color: white;}
    </style>
<?php $__env->stopPush(); ?>
<?php $__env->startSection('content'); ?>
    <section class="create_new_product">
        <div class="container custom_container">
            <div class="row">
                <div class="col-md-12">
                    <form id="productForm" method="post" action="<?php echo e(route('products.store')); ?>"  enctype="multipart/form-data">
                        <?php echo csrf_field(); ?>
                        <input type="hidden" name="timestamp" value="<?php echo e(time()); ?>">
                        <div class="box_shadow_wrapper">
                            <div class="row row_gap50">
                                <div class="col-md-12">
                                    <h2>Creating New Product</h2>
                                </div>
                                <div class="col-md-12">
                                    <div class="login_input_wrapper custom_drop_zone">
                                        <div class="dropzone" name="my_dropzone" id="my-dropzone"></div>
                                        <span id="dropzoneError" style="color: red; font-weight: bold;"></span>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <h2>Product Details</h2>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="">Product Name*</label>
                                                <input type="text" placeholder="Enter Product Name" class="form-control no-first-space" name="name" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="">SKU ID*</label>
                                                <input type="text" placeholder="SKU-EOC-0915" class="form-control no-first-space" value="SKU-<?php echo e(time()); ?>" name="sku_id" required>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="">Category*</label>
                                                <select name="main_category_id" id="main_category_id" class="form-select" data-control="select2" data-close-on-select="false" data-placeholder="Select Category" data-allow-clear="true"  required>
                                                    <option value="" selected disabled>Select Category</option>
                                                    <?php $__currentLoopData = $categories; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $category): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($category->id); ?>"><?php echo e($category->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group ">
                                                <label for="">Sub-Category*</label>
                                                <select name="category_id" id="category_id" class="form-select"  data-control="select2" data-close-on-select="false" data-placeholder="Select Parent Category First" data-allow-clear="true"  required>
                                                    <option value="" selected disabled>Select Parent Category First</option>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group custom_color_select">
                                                <label for="colorPicker">Colors*</label>
                                                <div id="colorPickerContainer" style="display: flex; gap: 10px; align-items: center;">
                                                    <input type="color" id="colorPicker" class=" custom_colors"/>
                                                    <button id="addColor" type="button" class="btn_maroon upload_button"><i class="fa-solid fa-plus"></i></button>
                                                </div>
                                                <div id="selectedColors" style="margin-top: 10px;"></div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group custom_size_select">
                                                <label for="">Sizes</label>
                                                <select class="form-select " data-control="select2" data-close-on-select="false" data-placeholder="Select an option" data-allow-clear="true" multiple="multiple" name="sizes[]">
                                                    <option></option>
                                                    <?php $__currentLoopData = $sizes; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $size): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                        <option value="<?php echo e($size->name); ?>"><?php echo e($size->name); ?></option>
                                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <div class="form-group custom_size_select">
                                                <label for="">Heading*</label>
                                                <input type="text" name="heading" id="heading" value="varaints" required class="form-control no-first-space">
                                            </div>
                                        </div>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="custom_flex">
                                                    <input type="checkbox" name="has_attributes" id="has_attributes" value="1" checked>
                                                    <label for="has_attributes">Custom Attributes</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="attributes_fields">
                                            <!-- First row -->
                                            <div class="attribute-row d-flex align-items-end">
                                                <div class="form-group ">
                                                    <label>Name *</label>
                                                    <input type="text" class="form-control no-first-space" name="attributes[0][name]" placeholder="Attribute Name (e.g., RAM)" required>
                                                </div>
                                                <div class="form-group ">
                                                    <label>Value *</label>
                                                    <input type="text" class="form-control no-first-space" name="attributes[0][value]" placeholder="Attribute Value (e.g., 8GB)" required>
                                                </div>
                                                <div class="custom_append_button">
                                                    <label><br></label>
                                                    <button type="button" id="add_attribute" class="btn_maroon custom_button"><i class="fa-solid fa-plus"></i></button>
                                                </div>
                                            </div>
                                        </div>
                                        <hr>
                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <div class="custom_flex">
                                                    <input type="hidden" name="has_variants" value="1">
                                                    
                                                    <label for="has_variants">Product variants (At least one variant is required)</label>
                                                </div>
                                            </div>
                                        </div>
                                        <div id="variant_fields" style="">
                                            <!-- First row -->
                                            <div class="variant-row d-flex align-items-end">
                                                <div class="form-group ">
                                                    <label>Option *</label>
                                                    <input type="text" class="form-control no-first-space" name="variants[0][name]" placeholder="Variant Option (e.g., RAM)" required>
                                                </div>
                                                <div class="form-group ">
                                                    <label>Value *</label>
                                                    <input type="text" class="form-control no-first-space" name="variants[0][value]" placeholder="Variant Value (e.g., 8GB)" required>
                                                </div>
                                                <div class="form-group ">
                                                    <label>Price *</label>
                                                    <input type="text" class="form-control no-first-space" name="variants[0][price]" min="0.01" placeholder="Variant Price (e.g., 1000)" required>
                                                </div>
                                                <div class="form-group ">
                                                    <label>Stock  Quantity *</label>
                                                    <input type="number" class="form-control no-first-space" name="variants[0][stock]" min="1" placeholder="Stock Quantity" required>
                                                </div>
                                                <div class="custom_append_button">
                                                    <label><br></label>
                                                    <button type="button" id="add_variant" class="btn_maroon custom_button"><i class="fa-solid fa-plus"></i></button>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="col-md-12">
                                            <div class="form-group">
                                                <label for="">Description*</label>
                                                <textarea class="form-control no-first-space" placeholder="Minimum 100 Characters" rows="4" name="description"></textarea>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="row custom_row">
                                        <div class="col-md-12">
                                            <div class="offer_checkbox">
                                                <input type="checkbox" id="shipping_fee" value="1" name="shipping_fee" >
                                                <label for="shipping_fee" >Offer Free Shipping for This Product</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="row custom_row">
                                        <div class="col-md-6">
                                            <div class="row">
                                                <div class="col-md-12">
                                                    <div class="form-group status_check_label">
                                                        <label for="">Status</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="status_checkbox">
                                                        <input type="radio" id="active" name="status" class="checkbox_input" value="1" checked>
                                                        <label for="active">Active</label>
                                                    </div>
                                                </div>
                                                <div class="col-md-6">
                                                    <div class="status_checkbox">
                                                        <input type="radio" id="Inactive" name="status" class="checkbox_input" value="0">
                                                        <label for="Inactive">Inactive</label>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="col-md-12">
                                    <div class="custom_justify">
                                        <button type="button" class="btn_light">Cancel <i class="fa-solid fa-x"></i></button>
                                        <button type="submit" class="btn_maroon">Create <i class="fa-solid fa-plus"></i></button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </section>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('js'); ?>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/dropzone/5.9.3/dropzone.min.js" referrerpolicy="no-referrer"></script>
    <script>
        Dropzone.autoDiscover = false;
        const myDropzone = new Dropzone("#my-dropzone", {
            url: "<?php echo e(url('upload-dropzone-files')); ?>",
            method: "post",
            headers: {
                'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>",
            },
            params: {
                'timestamp': "<?php echo e(time()); ?>",
            },
            paramName: "file",
            maxFilesize: 6,
            maxFiles: 25,
            acceptedFiles: ".jpg,.jpeg,.png",
            dictDefaultMessage: '<img src="<?php echo e(asset('website')); ?>/assets/images/dropzone_image_icon.svg"><h5>Drag & drop or click to upload document</h5> <p>Images upload limit: 25</p>',
            addRemoveLinks: true,
            dictRemoveFile: "Remove",
            previewTemplate: `
            <div class="dz-preview dz-file-preview">
                <div class="dz-image">
                    <img data-dz-thumbnail />
                    <span class="dz-set-primary">
                        <input type="radio" name="primaryImage" value="1" class="primary-radio" hidden />
                        <i class="fa-regular fa-star" title="Set as primary" data-bs-toggle="tooltip" data-bs-placement="top"></i>
                    </span>
                    <span class="dz-remove" data-dz-remove>
                        <i class="fa-solid fa-x" data-bs-toggle="tooltip" data-bs-placement="top" title="Remove image"></i>
                    </span>
                </div>
            </div>
        `,
            init: function() {
                let isAutoRemoving = false;  // flag to mark programmatic removal

                this.on("addedfile", function (file) {
                    if (this.files.length > 25) {
                        isAutoRemoving = true;   // mark as programmatic removal
                        this.removeFile(file);
                        Swal.fire({
                            title: "Limit Reached!",
                            html: "Maximum <strong>25 images</strong> allowed.<br>Please remove an image to add another.",
                            icon: "warning",
                            timer: 4000,
                            timerProgressBar: true
                        });
                        return;
                    }

                    // rest of your existing addedfile code...
                    if (document.querySelectorAll('.dz-set-primary.selected').length === 0) {
                        const primaryElement = file.previewElement.querySelector('.dz-set-primary');
                        primaryElement.classList.add("selected");
                        primaryElement.querySelector('i').classList.replace("fa-regular", "fa-solid");
                        primaryElement.querySelector('.primary-radio').checked = true;
                    }

                    file.previewElement.querySelector('.dz-set-primary').addEventListener("click", () => {
                        changePrimaryImage(file.previewElement);
                    });
                });

                this.on("removedfile", function (file) {
                    if (isAutoRemoving) {
                        // Reset flag and skip confirmation dialog on programmatic removal
                        isAutoRemoving = false;
                        return;
                    }

                    const isPrimary = file.previewElement.querySelector('.primary-radio').checked;
                    const imageId = file.previewElement.getAttribute('data-imageid');

                    Swal.fire({
                        title: 'Confirm Deletion',
                        text: 'Are you sure you want to delete this picture? This action cannot be undone.',
                        showCancelButton: true,
                        confirmButtonColor: '#C50000',
                        cancelButtonColor: '#F3EFEC',
                        cancelButtonText: 'Cancel <i class="fa-solid fa-xmark"></i>',
                        confirmButtonText: 'Yes <i class="fa-solid fa-check"></i>'
                    }).then((result) => {
                        if (result.isConfirmed) {
                            deleteFileFromDatabase(imageId);

                            if (isPrimary) {
                                const remainingFiles = document.querySelectorAll('.dz-preview');
                                if (remainingFiles.length > 0) {
                                    const firstFile = remainingFiles[0];
                                    changePrimaryImage(firstFile);
                                }
                            }
                        } else {
                            this.addFile(file);
                        }
                    });
                });

                // other event handlers remain unchanged...
            }
        });

        function changePrimaryImage(newPrimary) {
            const currentPrimary = document.querySelector('.dz-set-primary.selected');
            if (currentPrimary) {
                currentPrimary.classList.remove("selected");
                currentPrimary.querySelector('i').classList.replace("fa-solid", "fa-regular");
                currentPrimary.querySelector('.primary-radio').checked = false;

                const currentImageId = currentPrimary.closest('.dz-preview').getAttribute('data-imageid');
                updatePrimaryStatus(currentImageId, 0);
            }

            const newPrimarySet = newPrimary.querySelector('.dz-set-primary');
            newPrimarySet.classList.add("selected");
            newPrimarySet.querySelector('i').classList.replace("fa-regular", "fa-solid");
            newPrimarySet.querySelector('.primary-radio').checked = true;

            const newImageId = newPrimary.closest('.dz-preview').getAttribute('data-imageid');
            updatePrimaryStatus(newImageId, 1);
        }

        function updatePrimaryStatus(imageId, isPrimary) {
            fetch("<?php echo e(url('update-primary-status')); ?>", {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>",
                },
                body: JSON.stringify({ image_id: imageId, is_primary: isPrimary })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('Primary status updated successfully');
                    } else {
                        console.error('Failed to update primary status');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        function deleteFileFromDatabase(imageId) {
            fetch("<?php echo e(url('image-delete')); ?>/" + imageId, {
                method: "POST",
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRF-TOKEN': "<?php echo e(csrf_token()); ?>",
                },
                body: JSON.stringify({ image_id: imageId })
            })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log('File deleted from database successfully');
                    } else {
                        console.error('Failed to delete file from database');
                    }
                })
                .catch(error => {
                    console.error('Error:', error);
                });
        }

        $("#productForm").on("submit", function (e) {
            if (myDropzone.files.length === 0) {
                e.preventDefault();
                $("#dropzoneError").text("Please upload at least one image.");
                return false;
            }
        });

        myDropzone.on("addedfile", function () {
            $("#dropzoneError").text("");
        });

        $(document).ready(function() {
            $('.file_input').on('change', function(event) {
                const file = event.target.files[0];
                if (file) {
                    const fileName = file.name;
                    const fileDisplay = $('<div class="file-display-item"></div>');
                    const fileIcon = $('<i class="fa-solid fa-file-pdf pdf_file_icon"></i>');
                    const fileNameElement = $('<span class="file-name"></span>').text(fileName);
                    const crossIcon = $('<span class="cross-icon"></span>').html('<i class="fa-solid fa-x"></i>');
                    fileDisplay.append(fileIcon).append(fileNameElement).append(crossIcon);
                    $('.fileDisplay').append(fileDisplay);
                    crossIcon.on('click', function() {
                        fileDisplay.remove();
                    });
                }
            });
        });

        $(document).on('change', '#main_category_id', function() {
            var main_category_id = $(this).val();
            $.ajax({
                url: "<?php echo e(url('get-sub-categories')); ?>/"+main_category_id,
                type: "GET",
                success: function(response) {
                    $('#category_id').html(response);
                },
            });
        });
    </script>
    <script>
        $(function () {
            $('#addColor').click(function () {
                const selectedColor = $('#colorPicker').val();
                if ($(`#selectedColors div[data-color="${selectedColor}"]`).length){
                    return Swal.fire({
                        title: "OOPS!",
                        html: "Color is already added!",
                        icon: "warning",
                        timer: 5000,
                        buttons: false,
                    });
                }

                $('#selectedColors').append(`
                <div data-color="${selectedColor}" style="display:inline-flex;align-items:center;margin-right:5px;background-color:${selectedColor};color:#fff;padding:5px 10px;border-radius:5px;">
                    ${selectedColor}
                    <button style="margin-left:5px;background:transparent;border:none;color:#fff;cursor:pointer;">×</button>
                </div>
                <input type="hidden" name="colors[]" value="${selectedColor}" data-color="${selectedColor}" />
            `).find('div[data-color]').last().find('button').click(function () {
                    $(`input[data-color="${selectedColor}"]`).remove();
                    $(this).parent().remove();
                });
            });
        });


        $(function () {
            $('#has_variants').change(function () {
                if (this.checked) {
                    $('#variant_fields').show();
                    if (!$('#variant_fields .variant-row').length) {
                        addVariantRow(false);
                    }
                } else {
                    $('#variant_fields').hide().find('.variant-row').remove();
                }
            });
            $(document).on('click', '#add_variant', function () {
                addVariantRow(true);
            });
            $(document).on('click', '.remove-variant', function () {
                $(this).closest('.variant-row').remove();
            });
            let variantIndex = 0;
            function addVariantRow(includeRemove) {
                variantIndex++;
                const removeButton = includeRemove
                    ? `<button type="button" class="btn btn-danger btn-sm remove-variant custom_button"><i class="fa-solid fa-xmark"></i></button>`
                    : '<button type="button" id="add_variant" class="btn btn-primary btn-sm custom_button"><i class="fa-solid fa-plus"></i></button>';

                $('#variant_fields').append(`
            <div class="variant-row d-flex align-items-center gap-2 mb-2">
                <div class="form-group flex-grow-1 me-2">
                    <input type="text" class="form-control no-first-space " name="variants[${variantIndex}][name]" placeholder="Option (e.g., RAM)" required>
                </div>
                <div class="form-group flex-grow-1 me-2">
                    <input type="text" class="form-control no-first-space" name="variants[${variantIndex}][value]" placeholder="Value (e.g., 8GB)" required>
                </div>
                <div class="form-group flex-grow-1 me-2">
                    <input type="text" class="form-control no-first-space" min="0.01" name="variants[${variantIndex}][price]" placeholder="Price" required>
                </div>
                <div class="form-group flex-grow-1 me-2">
                    <input type="number" class="form-control no-first-space" min="1" name="variants[${variantIndex}][stock]" placeholder="Stock" required>
                </div>
                ${removeButton}
            </div>
        `);
            }
        });


        $(function () {
            $('#has_attributes').change(function () {
                if (this.checked) {
                    $('#attributes_fields').show();
                    if (!$('#attributes_fields .attribute-row').length) {
                        addAttributeRow(false);
                    }
                } else {
                    $('#attributes_fields').hide().find('.attribute-row').remove();
                }
            });

            $(document).on('click', '#add_attribute', function () {
                addAttributeRow(true);
            });

            $(document).on('click', '.remove-attribute', function () {
                $(this).closest('.attribute-row').remove();
            });
            let attributeIndex = 0;
            function addAttributeRow(includeRemove) {
                attributeIndex++;
                const removeButton = includeRemove
                    ? `<button type="button" class="btn btn-danger btn-sm remove-attribute custom_button"><i class="fa-solid fa-xmark"></i></button>`
                    : '<button type="button" id="add_attribute" class="btn btn-primary btn-sm custom_button"><i class="fa-solid fa-plus"></i></button>';

                $('#attributes_fields').append(`
            <div class="attribute-row d-flex align-items-center gap-2 mb-2">
                <div class="form-group flex-grow-1 me-2">
                    <input type="text" class="form-control no-first-space" name="attributes[${attributeIndex}][name]" placeholder="Attribute Name" required>
                </div>
                <div class="form-group flex-grow-1 me-2">
                    <input type="text" class="form-control no-first-space" name="attributes[${attributeIndex}][value]" placeholder="Attribute Value" required>
                </div>
                ${removeButton}
            </div>
        `);
            }
        });
    </script>
    <script>
        $(document).ready(function() {
            $('input[name="variants[0][price]"]').on('keypress', function(e) {
                let char = String.fromCharCode(e.which);

                if (!/[0-9.]/.test(char)) {
                    e.preventDefault();
                }

                if (char === '.' && $(this).val().indexOf('.') !== -1) {
                    e.preventDefault();
                }
            });

            $("#productForm").validate({
                rules: {
                    name: {
                        required: true
                    },
                    category_id: {
                        required: true,
                    },
                    heading: {
                        required: true,
                    },
                    "attributes[0][name]": {
                        required: true
                    },
                    "attributes[0][value]": {
                        required: true
                    },
                    "variants[0][name]": {
                        required: true
                    },
                    "variants[0][value]": {
                        required: true
                    },
                    "variants[0][price]": {
                        required: true,
                        number: true,
                        min: 0.01,
                    },

                    "variants[0][stock]": {
                        required: true,
                        number: true,
                    },
                    description: {
                        required: true,
                    }
                },
                messages: {
                    name: {
                        required: "Please enter a product name.",
                    },
                    category_id: {
                        required: "Please select a sub-category."
                    },
                    heading: {
                        required: "Please enter heading.",
                    },
                    "attributes[0][name]": {
                        required: "Attribute name is required."
                    },
                    "attributes[0][value]": {
                        required: "Attribute value is required."
                    },
                    "variants[0][name]": {
                        required: "Variant option is required."
                    },
                    "variants[0][value]": {
                        required: "Variant value is required."
                    },
                    "variants[0][price]": {
                        required: "Please enter a price.",
                        number: "Please enter a valid number.",
                    },
                    "variants[0][stock]": {
                        required: "Please enter stock quantity.",
                        number: "Please enter a valid number.",
                    },
                    description: {
                        required: "Please enter a product description.",
                    }
                },
                errorElement: "div",
                errorPlacement: function(error, element) {
                    error.addClass("text-danger");
                    element.closest(".form-group").append(error);
                },
                highlight: function(element) {
                    $(element).addClass("is-invalid");
                },
                unhighlight: function(element) {
                    $(element).removeClass("is-invalid");
                }
            });

            $("#productForm").on("submit", function(event) {
                if (!$(this).valid()) {
                    event.preventDefault();
                }
            });
        });
    </script>

    <script>
        $(document).ready(function() {
            $('[data-bs-toggle="tooltip"]').tooltip();
        });
    </script>
<?php $__env->stopPush(); ?>


<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/products/create.blade.php ENDPATH**/ ?>