<?php $__env->startPush('css'); ?>

<?php $__env->stopPush(); ?>
<?php $__env->startSection('navbar-title'); ?>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <?php if(auth()->user()->hasRole('admin')): ?>
        <section class="dashboard_index_pg_sec">
            <div class="container custom_container">
                <div class="row custom_row_gap">
                    <div class="col-md-3">
                        <div class="box_shadow_wrapper cards_wrapper">
                            <div class="total_user_wrapper_img">
                                <div class="total_user_img">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/total_user_icon.svg">
                                </div>
                            </div>
                            <div class="total_user_wrapper_details">
                                <h6>Total Users</h6>
                                <h2><?php echo e(number_format($users->count())); ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="box_shadow_wrapper cards_wrapper">
                            <div class="total_user_wrapper_img">
                                <div class="total_user_img">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/active_sellers.svg">
                                </div>
                            </div>
                            <div class="total_user_wrapper_details">
                                <h6>Active Sellers</h6>
                                <h2><?php echo e(@ $users->filter(fn($user) => $user->roles->contains('name', 'seller') && $user->status == 1)->count()); ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="box_shadow_wrapper cards_wrapper">
                            <div class="total_user_wrapper_img">
                                <div class="total_user_img">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/total-revenu.svg">
                                </div>
                            </div>
                            <div class="total_user_wrapper_details">
                                <h6>Total Revenue</h6>
                                <h2><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e(round($orders->where('status','completed')->sum('grand_total') / 1000, 1) . 'K'); ?></h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="box_shadow_wrapper cards_wrapper">
                            <div class="total_user_wrapper_img">
                                <div class="total_user_img">
                                    <img src="<?php echo e(asset('website')); ?>/assets/images/pending_payouts.svg">
                                </div>
                            </div>
                            <div class="total_user_wrapper_details">
                                <h6>Pending Payouts</h6>
                                <h2><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?>15,890.00</h2>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box_shadow_wrapper">
                            <div class="reviews_chart_wrapper">
                                <h2>Seller Leaderboard</h2>
                                <select class="form-select" id="yearSelect" aria-label="Default select">
                                    <option selected value="all">All</option>
                                    <?php $__currentLoopData = range(date('Y'), date('Y') - 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class=" chart_wrapper bar_chart_height">
                                <canvas class="chart_wrapper_order" id="yearly_sales_seller"  style="height:92%;width:100%"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box_shadow_wrapper">
                            <h2>Seller Leaderboard</h2>
                            <div class="table_wrapper seller_leaserboard_wrapper">
                                <table  class="table myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>Rank</th>
                                        <th>Name</th>
                                        <th>Amount</th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $users->filter(fn($user) => $user->roles->contains('name', 'seller')); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $user): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>#<?php echo e(rand(1,5)); ?></td>
                                            <td>
                                                <div class="table_user_name_wrapper">
                                                    <div>
                                                        <div class="table_user_img">
                                                            <img src="<?php echo e(asset('website')); ?>/<?php echo e($user->profile->pic); ?>">
                                                        </div>
                                                    </div>
                                                    <p><?php echo e($user->name); ?></p>
                                                </div></td>
                                            <td><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e($user->wallet->formatted_balance??0); ?></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="box_shadow_wrapper">
                            <h2>Recent Orders</h2>
                            <div class="table_wrapper">
                                <table  class="table myTable datatable">
                                    <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer Name</th>
                                        <th>Email</th>
                                        <th>Items</th>
                                        <th>Tracking ID</th>
                                        <th>Order Date</th>
                                        <th>Total Amount</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>#<?php echo e($order->unique); ?></td>
                                            <td>
                                                <div class="table_user_name_wrapper">
                                                    <div>
                                                        <div class="table_user_img">
                                                            <div class="table_user_img">
                                                                <?php if($order->buyer->profile->pic!=''): ?>
                                                                    <img src="<?php echo e(asset('website').'/'.$order->buyer->profile->pic); ?>" data-original-src="<?php echo e(asset('website').'/'.$order->buyer->profile->pic); ?>">
                                                                <?php else: ?>
                                                                    <img src="<?php echo e(asset('website')); ?>/assets/images/table_img_name.svg">
                                                                <?php endif; ?>
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <p><?php echo e(($order->name!="")?$order->name:$order->user->name); ?></p>
                                                </div>
                                            </td>
                                            <td><?php echo e(($order->email!="")?$order->email:$order->user->email); ?></td>
                                            <td><?php echo e($order->orderDetails->count()); ?>x</td>
                                            <td><?php echo e($order->tracking_number??"N/A"); ?></td>
                                            <td class="review-date"><?php echo e($order->created_at->format(env('GLOBAL_DATE_FORMAT'))); ?></td>
                                            <td><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e($order->grand_total); ?></td>
                                            <td>
                                                <div class="grey_status">
                                                    <i class="fa-solid fa-circle"></i>
                                                    <span><?php echo e(ucwords($order->status)); ?></span>
                                                </div>
                                            </td>
                                            <td><a href="<?php echo e(url('orders',$order->id)); ?>" class="table_td_veiw_icon"><i class="fa-regular fa-eye"></i></a></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>

                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php elseif(auth()->user()->hasRole('seller')): ?>
        <section class="dashboard_index_pg_sec">
            <div class="container custom_container">
                <div class="row custom_row_gap">
                    <div class="col-md-12">
                        <div class="seller_dashboard_wrapper_completed_orders">
                            <div class="box_shadow_wrapper cards_wrapper">
                                <div class="total_user_wrapper_img">
                                    <div class="total_user_img">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/completed_orders_order_reports.svg">
                                    </div>
                                </div>
                                <div class="total_user_wrapper_details">
                                    <h6>Completed Orders</h6>
                                    <h2><?php echo e($orders->where('status','completed')->count()??0); ?></h2>
                                </div>
                            </div>
                            <div class="box_shadow_wrapper cards_wrapper">
                                <div class="total_user_wrapper_img">
                                    <div class="total_user_img">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/ic1.svg">
                                    </div>
                                </div>
                                <div class="total_user_wrapper_details">
                                    <h6>Pending Orders</h6>
                                    <h2><?php echo e($orders->where('status','pending')->count()??0); ?></h2>
                                </div>
                            </div>
                            <div class="box_shadow_wrapper cards_wrapper">
                                <div class="total_user_wrapper_img">
                                    <div class="total_user_img">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/to_ship_seller.svg">
                                    </div>
                                </div>
                                <div class="total_user_wrapper_details">
                                    <h6>Shipped <img src="<?php echo e(asset('website')); ?>/assets/images/arrow-up-right_orange_seller.svg"></h6>
                                    <h2><?php echo e($orders->where('status','shipped')->count()??0); ?></h2>
                                </div>
                            </div>
                            <div class="box_shadow_wrapper cards_wrapper">
                                <div class="total_user_wrapper_img">
                                    <div class="total_user_img">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/ic3.svg">
                                    </div>
                                </div>
                                <div class="total_user_wrapper_details">
                                    <h6>Canceled Orders</h6>
                                    <h2><?php echo e($orders->where('status','canceled')->count()??0); ?></h2>
                                </div>
                            </div>
                            <div class="box_shadow_wrapper cards_wrapper">
                                <div class="total_user_wrapper_img">
                                    <div class="total_user_img">
                                        <img src="<?php echo e(asset('website')); ?>/assets/images/total-revenu.svg">
                                    </div>
                                </div>
                                <div class="total_user_wrapper_details">
                                    <h6>Total Revenue</h6>
                                    <h2><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e(round($orders->where('status','completed')->sum('grand_total') / 1000, 1) . 'K'); ?></h2>
                                </div>

                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box_shadow_wrapper">
                            <div class="reviews_chart_wrapper">
                                <h2>Orders Overview</h2>
                                <select class="form-select" id="yearSelect" aria-label="Default select ">
                                    <option selected value="all">All</option>
                                    <?php $__currentLoopData = range(date('Y'), date('Y') - 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="chart_wrapper bar_chart_height">
                                <canvas class="chart_wrapper_order" id="yearly_sales_order"  style=""></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="box_shadow_wrapper">
                            <div class="reviews_chart_wrapper">
                                <h2>Most Sold Products</h2>
                                <select class="form-select" id="selectYear" aria-label="Default select ">
                                    <option selected value="all">All</option>
                                    <?php $__currentLoopData = range(date('Y'), date('Y') - 5); $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $year): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($year); ?>"><?php echo e($year); ?></option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                </select>
                            </div>
                            <div class="chart_wrapper chart_no_image_found">
                                <div id="noDataMessage" class="no_data_found_img" style="display: none; text-align: center;">
                                    <lottie-player
                                        src="https://assets6.lottiefiles.com/packages/lf20_mxuufmel.json"
                                        background="transparent"
                                        speed="1"
                                        style="width: 40%; height: 100%; margin: auto;"
                                        loop
                                        autoplay>
                                    </lottie-player>
                                </div>
                                <canvas id="most_sold_products"></canvas>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="box_shadow_wrapper">
                            <h2>Recent Orders</h2>
                            <div class="table_wrapper">
                                <table  class="table myTable datatable customers">
                                    <thead>
                                    <tr>
                                        <th>Order ID</th>
                                        <th>Customer Name</th>
                                        <th>Email</th>
                                        <th>Items</th>
                                        <th>Tracking ID</th>
                                        <th>Order Date</th>
                                        <th>Total Amount</th>
                                        <th>Status</th>
                                        <th></th>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    <?php $__currentLoopData = $orders; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $order): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                        <tr>
                                            <td>#<?php echo e($order->unique); ?></td>
                                            <td>
                                                <div class="table_user_name_wrapper">
                                                    <div>
                                                        <div class="table_user_img">
                                                            <?php if($order->buyer->profile->pic!=''): ?>
                                                                <img src="<?php echo e(asset('website').'/'.$order->buyer->profile->pic); ?>" data-original-src="<?php echo e(asset('website').'/'.$order->buyer->profile->pic); ?>">
                                                            <?php else: ?>
                                                                <img src="<?php echo e(asset('website')); ?>/assets/images/table_img_name.svg">
                                                            <?php endif; ?>
                                                        </div>
                                                    </div>
                                                    <p><?php echo e(($order->name!="")?ucwords($order->name):ucwords($order->buyer->name)); ?></p>
                                                </div>
                                            </td>
                                            <td><?php echo e(($order->email!="")?$order->email:$order->buyer->email); ?></td>
                                            <td><?php echo e($order->orderDetails->count()); ?>x</td>
                                            <td><?php echo e($order->tracking_number??"N/A"); ?></td>
                                            <td class="review-date"><?php echo e($order->created_at->format(env('GLOBAL_DATE_FORMAT'))); ?></td>
                                            <td><?php echo e(env('ACTIVE_CURRENCY_SYMBOL')); ?><?php echo e($order->grand_total); ?></td>
                                            <td>
                                                <div class="grey_status">
                                                    <i class="fa-solid fa-circle"></i>
                                                    <span><?php echo e(ucwords($order->status)); ?></span>
                                                </div>
                                            </td>
                                            <td><a href="<?php echo e(url('orders',$order->id)); ?>" class="table_td_veiw_icon"><i class="fa-regular fa-eye"></i></a></td>
                                        </tr>
                                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </tbody>
                                </table>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </section>
    <?php endif; ?>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('js'); ?>
    
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <?php if(auth()->user()->hasRole('admin')): ?>
        <?php
        $monthlyUsers = collect($users)
            ->groupBy(fn($users) => \Carbon\Carbon::parse($users->created_at)->format('M'))
            ->map->count();

        $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        $monthlyData = array_map(fn($month) => $monthlyUsers[$month] ?? 0, $months);
        ?>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const ctx = document.getElementById('yearly_sales_seller').getContext('2d');
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, '#F05660');
            gradient.addColorStop(1, '#790008');

            let chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($months); ?>,
                    datasets: [{
                        data: <?php echo json_encode($monthlyData); ?>,
                        backgroundColor: gradient,
                        borderColor: '#790008',
                        borderWidth: 0,
                        borderRadius: 200,
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: { display: false },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                precision: 0
                            }
                        }
                    }
                }
            });

            document.getElementById('yearSelect').addEventListener('change', function () {
                const selectedYear = this.value;

                fetch(`/get-users-data/${selectedYear}`)
                    .then(response => response.json())
                    .then(data => {
                        const monthlyUsers = data.monthlyUsers;
                        const monthlyData = data.months.map(month => monthlyUsers[month] || 0);

                        chart.data.datasets[0].data = monthlyData;
                        chart.update();
                    })
                    .catch(error => console.error('Error fetching data:', error));
            });
        });

    </script>

    <?php endif; ?>
    
    <?php if(auth()->user()->hasRole('seller')): ?>
        <script src="https://unpkg.com/@lottiefiles/lottie-player@latest"></script>

        <?php
            $monthlyOrders = collect($orders)
                ->groupBy(fn($orders) => \Carbon\Carbon::parse($orders->created_at)->format('M'))
                ->map->count();

            $months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            $monthlyData = array_map(fn($month) => $monthlyOrders[$month] ?? 0, $months);
        ?>
    <script>
        <?php
            $topProducts = collect($orders)->flatMap->orderDetails
                ->groupBy('product.name')
                ->map->sum('quantity')
                ->sortDesc()
                ->take(5);
        ?>
        document.addEventListener('DOMContentLoaded', function () {
            let ctx = document.getElementById("most_sold_products").getContext('2d');
            let chart;

            function fetchAndUpdateChart(year) {
                    fetch(`<?php echo e(url('')); ?>/get-top-products/${year}`)
                    .then(response => response.json())
                    .then(data => {

                        if (chart) {
                            chart.destroy();
                        }

                        if (data.labels.length === 0 || data.data.length === 0) {
                            noDataMessage.style.display = "block";
                            return;
                        } else {
                            noDataMessage.style.display = "none";
                        }
                        chart = new Chart(ctx, {
                            type: 'doughnut',
                            data: {
                                labels: data.labels,
                                datasets: [{
                                    data: data.data,
                                    borderColor: ['#6C3DBD', '#C12A6D', '#7B1400', '#F76447'],
                                    backgroundColor: ['#6C3DBD', '#C12A6D', '#7B1400', '#F76447'],
                                    borderWidth: 1
                                }]
                            },
                            options: {
                                responsive: true,
                                maintainAspectRatio: false,
                                plugins: {
                                    legend: {
                                        labels: {
                                            usePointStyle: true,
                                            pointStyle: 'circle',
                                        }
                                    }
                                }
                            }
                        });
                    })
                    .catch(error => console.error('Error fetching data:', error));
            }

            document.getElementById('selectYear').addEventListener('change', function () {
                fetchAndUpdateChart(this.value);
            });

            fetchAndUpdateChart(document.getElementById('selectYear').value);
        });


        document.addEventListener('DOMContentLoaded', function () {
            const ctx = document.getElementById('yearly_sales_order').getContext('2d');
            const gradient = ctx.createLinearGradient(0, 0, 0, 400);
            gradient.addColorStop(0, '#F05660');
            gradient.addColorStop(1, '#790008');

            let chart = new Chart(ctx, {
                type: 'bar',
                data: {
                    labels: <?php echo json_encode($months); ?>,
                    datasets: [{
                        data: <?php echo json_encode($monthlyData); ?>,
                        backgroundColor: gradient,
                        borderColor: '#790008',
                        borderWidth: 0,
                        borderRadius: 200,
                    }]
                },
                options: {
                    plugins: {
                        legend: { display: false },
                    },
                    scales: {
                        y: {
                            beginAtZero: true,
                            ticks: {
                                stepSize: 1,
                                precision: 0
                            }
                        }
                    }
                }
            });

            document.getElementById('yearSelect').addEventListener('change', function () {
                const selectedYear = this.value;

                fetch(`<?php echo e(url('get-orders-data')); ?>/${selectedYear}`)
                    .then(response => response.json())
                    .then(data => {
                        const monthlyOrders = data.monthlyOrders;
                        const monthlyData = data.months.map(month => monthlyOrders[month] || 0);

                        chart.data.datasets[0].data = monthlyData;
                        chart.update();
                    })
                    .catch(error => console.error('Error fetching data:', error));
            });
        });

    </script>
    <?php endif; ?>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('theme.layout.master', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\xampp\htdocs\bosque.uk.live\bosque_main_files\resources\views/dashboard/index.blade.php ENDPATH**/ ?>