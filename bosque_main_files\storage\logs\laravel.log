[2025-07-08 04:39:19] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-07-08 04:39:19] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#19 {main}
"} 
[2025-07-08 04:55:04] local.ERROR: Call to undefined function App\Http\Controllers\redierct() {"userId":208,"exception":"[object] (Error(code: 0): Call to undefined function App\\Http\\Controllers\\redierct() at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php:130)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->store(Object(App\\Http\\Requests\\ProductDiscountRequest))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'store')
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-08 04:55:40] local.ERROR: Call to undefined function App\Http\Controllers\redierct() {"userId":208,"exception":"[object] (Error(code: 0): Call to undefined function App\\Http\\Controllers\\redierct() at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php:130)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->store(Object(App\\Http\\Requests\\ProductDiscountRequest))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'store')
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-08 04:56:53] local.ERROR: Call to undefined function App\Http\Controllers\redierct() {"userId":208,"exception":"[object] (Error(code: 0): Call to undefined function App\\Http\\Controllers\\redierct() at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php:130)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->store(Object(App\\Http\\Requests\\ProductDiscountRequest))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'store')
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-08 04:57:01] local.ERROR: Call to undefined function App\Http\Controllers\redierct() {"userId":208,"exception":"[object] (Error(code: 0): Call to undefined function App\\Http\\Controllers\\redierct() at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php:130)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->store(Object(App\\Http\\Requests\\ProductDiscountRequest))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('store', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'store')
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#58 {main}
"} 
[2025-07-08 05:00:40] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' (Connection: mysql, SQL: update `product_discounts` set `discount_quantity` = ?, `product_discounts`.`updated_at` = 2025-07-08 05:00:40 where `id` = 9) {"userId":208,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' (Connection: mysql, SQL: update `product_discounts` set `discount_quantity` = ?, `product_discounts`.`updated_at` = 2025-07-08 05:00:40 where `id` = 9) at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:795)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('update `product...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(593): Illuminate\\Database\\Connection->run('update `product...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('update `product...', Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3417): Illuminate\\Database\\Connection->update('update `product...', Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1042): Illuminate\\Database\\Query\\Builder->update(Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1213): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1130): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->update(Object(Illuminate\\Http\\Request), '9')
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('update', Array)
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'update')
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:601)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(601): PDO->prepare('update `product...')
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(788): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('update `product...', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('update `product...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(593): Illuminate\\Database\\Connection->run('update `product...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('update `product...', Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3417): Illuminate\\Database\\Connection->update('update `product...', Array)
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1042): Illuminate\\Database\\Query\\Builder->update(Array)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1213): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1130): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->update(Object(Illuminate\\Http\\Request), '9')
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('update', Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'update')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}
"} 
[2025-07-08 05:00:54] local.ERROR: SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' (Connection: mysql, SQL: update `product_discounts` set `discount_quantity` = 1, `product_discounts`.`updated_at` = 2025-07-08 05:00:54 where `id` = 9) {"userId":208,"exception":"[object] (Illuminate\\Database\\QueryException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' (Connection: mysql, SQL: update `product_discounts` set `discount_quantity` = 1, `product_discounts`.`updated_at` = 2025-07-08 05:00:54 where `id` = 9) at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:795)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('update `product...', Array, Object(Closure))
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(593): Illuminate\\Database\\Connection->run('update `product...', Array, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('update `product...', Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3417): Illuminate\\Database\\Connection->update('update `product...', Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1042): Illuminate\\Database\\Query\\Builder->update(Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1213): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1130): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->update(Object(Illuminate\\Http\\Request), '9')
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('update', Array)
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'update')
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#62 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#64 {main}

[previous exception] [object] (PDOException(code: 42S22): SQLSTATE[42S22]: Column not found: 1054 Unknown column 'discount_quantity' in 'field list' at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php:601)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(601): PDO->prepare('update `product...')
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(788): Illuminate\\Database\\Connection->Illuminate\\Database\\{closure}('update `product...', Array)
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(755): Illuminate\\Database\\Connection->runQueryCallback('update `product...', Array, Object(Closure))
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(593): Illuminate\\Database\\Connection->run('update `product...', Array, Object(Closure))
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Connection.php(545): Illuminate\\Database\\Connection->affectingStatement('update `product...', Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Query\\Builder.php(3417): Illuminate\\Database\\Connection->update('update `product...', Array)
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Builder.php(1042): Illuminate\\Database\\Query\\Builder->update(Array)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1213): Illuminate\\Database\\Eloquent\\Builder->update(Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Database\\Eloquent\\Model.php(1130): Illuminate\\Database\\Eloquent\\Model->performUpdate(Object(Illuminate\\Database\\Eloquent\\Builder))
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Controllers\\ProductDiscountsController.php(185): Illuminate\\Database\\Eloquent\\Model->save()
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php(54): App\\Http\\Controllers\\ProductDiscountsController->update(Object(Illuminate\\Http\\Request), '9')
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php(43): Illuminate\\Routing\\Controller->callAction('update', Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(259): Illuminate\\Routing\\ControllerDispatcher->dispatch(Object(Illuminate\\Routing\\Route), Object(App\\Http\\Controllers\\ProductDiscountsController), 'update')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php(205): Illuminate\\Routing\\Route->runController()
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(799): Illuminate\\Routing\\Route->run()
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Routing\\Router->Illuminate\\Routing\\{closure}(Object(Illuminate\\Http\\Request))
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\spatie\\laravel-permission\\src\\Middlewares\\PermissionMiddleware.php(24): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Spatie\\Permission\\Middlewares\\PermissionMiddleware->handle(Object(Illuminate\\Http\\Request), Object(Closure), 'productdiscount...')
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SumsubVerification.php(15): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SumsubVerification->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\PreventBackHistory.php(13): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\PreventBackHistory->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Middleware\\SubstituteBindings.php(50): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Routing\\Middleware\\SubstituteBindings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Auth\\Middleware\\Authenticate.php(57): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Auth\\Middleware\\Authenticate->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken.php(78): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\View\\Middleware\\ShareErrorsFromSession.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\View\\Middleware\\ShareErrorsFromSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(121): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Session\\Middleware\\StartSession.php(64): Illuminate\\Session\\Middleware\\StartSession->handleStatefulRequest(Object(Illuminate\\Http\\Request), Object(Illuminate\\Session\\Store), Object(Closure))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Session\\Middleware\\StartSession->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse.php(37): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Cookie\\Middleware\\EncryptCookies.php(67): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Cookie\\Middleware\\EncryptCookies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#44 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#45 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#46 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#47 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#48 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#49 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#50 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#51 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#52 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#53 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#54 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#55 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#56 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#57 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#58 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#59 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#60 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#61 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#62 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#63 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#64 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#65 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#66 {main}
"} 
