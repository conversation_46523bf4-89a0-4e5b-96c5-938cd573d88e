[2025-07-08 04:39:19] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(169): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(798): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#19 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(777): Illuminate\\Routing\\Router->runRouteWithinStack(Object(Illuminate\\Routing\\Route), Object(Illuminate\\Http\\Request))
#20 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(741): Illuminate\\Routing\\Router->runRoute(Object(Illuminate\\Http\\Request), Object(Illuminate\\Routing\\Route))
#21 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Router.php(730): Illuminate\\Routing\\Router->dispatchToRoute(Object(Illuminate\\Http\\Request))
#22 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(200): Illuminate\\Routing\\Router->dispatch(Object(Illuminate\\Http\\Request))
#23 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(141): Illuminate\\Foundation\\Http\\Kernel->Illuminate\\Foundation\\Http\\{closure}(Object(Illuminate\\Http\\Request))
#24 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\app\\Http\\Middleware\\SetTimezone.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#25 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): App\\Http\\Middleware\\SetTimezone->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#26 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#27 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull.php(31): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#28 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#29 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest.php(21): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#30 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\TrimStrings.php(40): Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#31 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\TrimStrings->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#32 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize.php(27): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#33 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#34 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance.php(86): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#35 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#36 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\HandleCors.php(49): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#37 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\HandleCors->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#38 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Http\\Middleware\\TrustProxies.php(39): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#39 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(180): Illuminate\\Http\\Middleware\\TrustProxies->handle(Object(Illuminate\\Http\\Request), Object(Closure))
#40 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Pipeline\\Pipeline.php(116): Illuminate\\Pipeline\\Pipeline->Illuminate\\Pipeline\\{closure}(Object(Illuminate\\Http\\Request))
#41 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(175): Illuminate\\Pipeline\\Pipeline->then(Object(Closure))
#42 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(144): Illuminate\\Foundation\\Http\\Kernel->sendRequestThroughRouter(Object(Illuminate\\Http\\Request))
#43 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(52): Illuminate\\Foundation\\Http\\Kernel->handle(Object(Illuminate\\Http\\Request))
#44 {main}
"} 
[2025-07-08 04:39:19] production.ERROR: No application encryption key has been specified. {"exception":"[object] (Illuminate\\Encryption\\MissingAppKeyException(code: 0): No application encryption key has been specified. at E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php:79)
[stacktrace]
#0 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Support\\helpers.php(307): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(NULL)
#1 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(77): tap(NULL, Object(Closure))
#2 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(60): Illuminate\\Encryption\\EncryptionServiceProvider->key(Array)
#3 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Encryption\\EncryptionServiceProvider.php(32): Illuminate\\Encryption\\EncryptionServiceProvider->parseKey(Array)
#4 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(908): Illuminate\\Encryption\\EncryptionServiceProvider->Illuminate\\Encryption\\{closure}(Object(Illuminate\\Foundation\\Application), Array)
#5 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build(Object(Closure))
#6 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('encrypter', Array, true)
#7 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('encrypter', Array)
#8 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('encrypter', Array)
#9 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(1066): Illuminate\\Foundation\\Application->make('encrypter')
#10 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(982): Illuminate\\Container\\Container->resolveClass(Object(ReflectionParameter))
#11 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(943): Illuminate\\Container\\Container->resolveDependencies(Array)
#12 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(795): Illuminate\\Container\\Container->build('App\\\\Http\\\\Middle...')
#13 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(933): Illuminate\\Container\\Container->resolve('App\\\\Http\\\\Middle...', Array, true)
#14 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Container\\Container.php(731): Illuminate\\Foundation\\Application->resolve('App\\\\Http\\\\Middle...', Array)
#15 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Application.php(918): Illuminate\\Container\\Container->make('App\\\\Http\\\\Middle...', Array)
#16 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(255): Illuminate\\Foundation\\Application->make('App\\\\Http\\\\Middle...')
#17 E:\\xampp\\htdocs\\bosque.uk.live\\bosque_main_files\\vendor\\laravel\\framework\\src\\Illuminate\\Foundation\\Http\\Kernel.php(213): Illuminate\\Foundation\\Http\\Kernel->terminateMiddleware(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#18 E:\\xampp\\htdocs\\bosque.uk.live\\index.php(56): Illuminate\\Foundation\\Http\\Kernel->terminate(Object(Illuminate\\Http\\Request), Object(Illuminate\\Http\\JsonResponse))
#19 {main}
"} 
