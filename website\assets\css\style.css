:root{
    --pink:#C12A6D;
    --text:#3E3E3E;
    --orange:#F76447;
    --green:#47A400;
    --white:#fff;
    --dark_grey_color:#727272;
}

@font-face {font-family: 'leagueSpartan-Bold';src: url('/website/assets/fonts/LeagueSpartan/LeagueSpartan-Bold.ttf');}
@font-face {font-family: 'leagueSpartan-Regular';src: url('/website/assets/fonts/LeagueSpartan/LeagueSpartan-Regular.ttf');}
@font-face {font-family: 'leagueSpartan-Medium';src: url('/website/assets/fonts/LeagueSpartan/LeagueSpartan-Medium.ttf');}
@font-face {font-family: 'leagueSpartan-Light';src: url('/website/assets/fonts/LeagueSpartan/LeagueSpartan-Light.ttf');}
@font-face {font-family: 'leagueSpartan-SemiBold';src: url('/website/assets/fonts/LeagueSpartan/LeagueSpartan-SemiBold.ttf');}

@font-face {font-family: 'Poppins-Regular';src: url('/website/assets/fonts/Poppins/Poppins-Regular.ttf');}
@font-face {font-family: 'PublicSans-Regular';src: url('/website/assets/fonts/Public_Sans/PublicSans-Regular.ttf');}


h1{color:var(--text);font-family: 'leagueSpartan-Light';font-size:90px;letter-spacing: -1.8px;margin:0}
h2{color:var(--text);font-family: 'leagueSpartan-Light';font-size:38px;letter-spacing: -0.76px;margin:0}
h3{color:var(--text);font-family: 'leagueSpartan-SemiBold';font-size:31px;margin:0}
h4{color:var(--text);font-family: 'leagueSpartan-Light';font-size:24px;letter-spacing: -0.48px;margin:0}
h5{color:var(--text);font-family: 'leagueSpartan-Medium';font-size:20px;line-height:23px;margin:0}
h6{color:var(--text);font-family: 'leagueSpartan-SemiBold';font-size:18px;line-height:21px;letter-spacing:0.42px;margin:0}
p{color:var(--text);font-family: 'leagueSpartan-Regular';font-size:14px;line-height: 18px;margin:0}
a , button{cursor: pointer;}
/*input[type="checkbox"]{accent-color: #C12A6D;}*/
.btn_maroon{display: flex;  align-items: center;  column-gap: 6px;  justify-content: center;border-radius: 4px;  background: var(--pink); padding: 15px;color: var(--white); font-family: 'LeagueSpartan-Regular';  font-size: 16px; border: 0px; }
.btn_maroon i{color: var(--white);font-size: 16px;}
.shadow_box{padding: 20px;border-radius: 10px;  background: #FFF;}
.btn_main{border-radius: 5px;padding:15px 30px;font-family: 'leagueSpartan-Regular';font-size:16px;line-height: normal}
.btn_orange{width:fit-content;padding: 15px 30px;background:var(--orange);color:var(--white);border: 0px;display: flex;  justify-content: center; column-gap: 15px;border-radius: 4px;}
.btn_pink{background:var(--pink);color:var(--white)}
.btn_main:has(i) i, .cart_btn:has(i) i{margin-left: 10px}

.cart_btn{background:var(--pink);color:var(--white);padding:10px 15px;font-family: 'leagueSpartan-Regular';font-size:14px;border-radius: 5px;border: 0px;display: flex;  justify-content: center; column-gap: 15px;}
 .txt_field input[type="text"],.txt_field input[type="tel"],.txt_field input[type="email"],.txt_field input[type="password"]{border-radius: 4px;background: #FAFAFA;padding:15px 10px ;border:none}
 .txt_field  label{color:var(--text);font-size:16px;margin-bottom:10px;font-family:"leagueSpartan-Regular";line-height:22px}
 .txt_field input[type="text"]::placeholder,.txt_field input[type="tel"]::placeholder,.txt_field input[type="email"]::placeholder,.txt_field input[type="password"]::placeholder{color:#8B909A;font-family: "LeagueSpartan-Regular";font-size:18px;line-height:22px}
.custom_flex{display:flex;align-items: center;gap:10px}
.custom_justify{display:flex;justify-content: space-between;align-items: center}
/*.custom_container{max-width:1280px}*/
.custom_container{max-width:1600px}
select:focus-visible,input:focus-visible {outline:none}
.custom_row__gap{row-gap:20px;}
input::-webkit-outer-spin-button,
input::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
}

/*.switch-toggle-checkbox:checked + .switch-toggle-label {*/
/*    background-color: var(--pink);*/
/*}*/
.popular_products_wrapper .nav-item:first-child {position: sticky;left: 0;margin-right:20px}
a.wishlist_icon{position:absolute;right:0}
.wishlist_icon.clicked svg{fill:#C12A6D;stroke:#C12A6D;}
.wishlist_icon svg{fill:#FFF;stroke:#000;}
/*main {padding: 0;max-width: 1920px;width: 100%;margin: 0 auto;}*/
/*Header*/
.upload_img_remove i{font-size:20px;}
/*.custom_file_upload{padding-top:30px;}*/
.append_type_file input[type="file"]{z-index: 1;opacity: 0;position: absolute;height: 100%;top: 0;left: 0;right: 0;cursor: pointer;display: unset;}
.append_type_wrapper .append_type_file {width: 100%;height: 100%;overflow: visible;border: 1px solid black;border-radius: 10px;display: flex;align-items: center;justify-content: center;}
.append_type_wrapper {position: relative;height: 200px;width: 100%;}
.append_img_div_remove {border: 0;position: absolute;right: -9px;top: -9px;background: unset;z-index: 99;}
.modal_footer {display:flex;justify-content:space-between;padding-top:30px;}
.append_img_div_remove i{color:#fff;background-color: #C12A6D;height: 20px;width: 20px;border-radius: 50%;padding: 10px;display: flex;align-items: center;justify-content: center;}
.upload_img_modal_btn{position:absolute;right:-10px;border:0px;background:unset;top:5%;}
.upload_img_modal_btn i{font-size:16px;color:#c12a6d}
.append_type_wrapper .image_preview{width:100%;height:100%;object-fit:contain;}
/*.navbar .main_navbar {margin-left:30px}*/
/*.navbar .resp_nav_buttons {margin-right:20px;display:flex;justify-content:space-between;width:100%;align-items:center}*/
#kt_body .select_your_role_page{height:100%}
body.loading{overflow: hidden;}
.custom_top_navbar .follow_us.custom_flex p{line-height: normal}
.custom_top_navbar{background: #F5F5F5;padding: 15px 0px}
.top_navbar {display: flex;align-items: center;gap: 20px;vertical-align: text-top;}
.top_navbar  .top_menu li {list-style-type: none;position: relative;padding-right: 20px}
.top_navbar  .top_menu li:after {content: "";position: absolute;right: 0;top: 0;width: 2px;height: 100%;background: var(--text)}
.top_navbar  .top_menu {display: flex;align-items: center;gap: 20px;margin:0;padding: 0px;}
.top_navbar  .top_menu li a {color: var(--text);font-family: 'leagueSpartan-Regular';font-size: 14px;line-height: normal}
.top_navbar  .follow_us.custom_flex .icons_div i{color:var(--text);font-size:14px;font-weight: 400}
 .icons_div  .login_image {width:100px;height:60px}
 .icons_div .login_image img{width:100%;height:100%;object-fit:contain}

/*navbar*/
.mega_menu_banner ul.dropdown-menu.show li a:hover{background-color:#C12A6D;color:white}
.mega_menu_banner .dropdown .dropdown-toggle .fa-chevron-down{font-size:14px;margin-left:10px}
.mega_menu .nav_link,.header_icons .custom_flex{position: relative;}
.mega_menu_banner .mega_menu_banner.mega_list.nav_item span.select2-dropdown.select2-dropdown--below {min-width: 140px;}
.mega_menu_banner .custom_mega_menu_list .dropdown-toggle:after{display:none}
.mega_menu .nav_link::after,.header_icons .custom_flex::after {content: '';position: absolute;left: 0;bottom: -10px;width: 0;height: 2px;background-color: #C12A6D;transition: width 0.3s ease;}
.mega_menu .nav_link:hover::after,.header_icons .custom_flex:hover::after {width: 100%;}
.btn_main { transition: transform 0.3s ease, box-shadow 0.3s ease;border:0px;}
/*.btn_main:hover { transform: scale(1.05);}*/
.navbar .my_nav{gap:30px}
.navbar .custom_nav{width: 100%}
.navbar .custom_nav .resp_logo{display: flex;justify-content: space-between;align-items: center}
.navbar .custom_nav .resp_logo .site_logo{width: 150px;height: 60px;display: none}
.navbar .custom_nav .navbar-toggler{float: right}
.navbar .show_cart_items_modal{position: relative;z-index: 99;}
.product_list_dropdown {position: absolute;width: 100%;background: white;border: 1px solid #ddd;list-style: none;padding: 0;display: none;}
.product_list_dropdown li {padding: 8px;cursor: pointer;}
.product_list_dropdown li:hover {background: #f0f0f0;}
.navbar  span.wishlist_count.get_wishlist_items_count,.messages_count {position:absolute;right:-10px;background:#c12a6d;width:15px;height:15px;border-radius:50%;display:flex;justify-content:center;align-items:center;font-size:10px;color:white;padding:8px;top:-12px}
.navbar .show_cart_items_modal span.cart_count {position:absolute;right:-10px;background:#c12a6d;width:15px;height:15px;border-radius:50%;display:flex;justify-content:center;align-items:center;font-size:10px;color:white;padding:8px;top:-12px}
.navbar .custom_dropdown .dropdown ul.dropdown-menu .dropdown-item,.mega_menu_banner ul.dropdown-menu .dropdown-item {white-space: normal;word-wrap: break-word;}
.navbar .custom_dropdown.custom_simple_select_dropdown .dropdown .custom_icon_dropdown{background-color:none;box-shadow:none;border:none ;padding:0;height:0}
.navbar .custom_dropdown.custom_simple_select_dropdown .dropdown .custom_icon_dropdown i{top:3px;font-size:14px}
.navbar .main_navbar.custom_flex{gap:30px;width:100%;flex-wrap:wrap}
.navbar .main_navbar.custom_flex .header_icons{flex:1}
.custom_product_search .product_search.txt_field input[type="text"]{background:none}
.navbar .main_navbar.custom_flex .custom_product_search{flex:3}
/*.navbar .main_navbar.custom_flex .site_logo{flex:1}*/
.navbar a.navbar-brand.site_logo {width:200px;height:60px;margin:0;padding:0}
.navbar a.navbar-brand.site_logo img{width:100%;height:100%;object-fit:contain}
.navbar.navbar-expand-lg {padding:10px 0px;border-bottom: 1px solid #DBDBDB}
.navbar .custom_product_search select, .navbar .custom_product_search  input{border:none;}
.navbar .custom_product_search  .product_search{position:relative;width:100%}
.navbar .custom_product_search  .product_search i.search_icon{position:absolute;right:30px;top:20%;color:#BDBDBD;font-size:12px;font-weight:900}
.navbar .custom_product_search .product_search.txt_field input{padding:0px 60px 0px 15px;}
.navbar .custom_product_search .product_search.txt_field input::placeholder{color:#BDBDBD;font-size:14px;font-family: 'leagueSpartan-Regular';}
.navbar .custom_product_search  {border-radius: 4px;border: 1px solid #C12A6D;padding:15px 20px;display:flex;align-items:center}
.navbar .custom_product_search  .product_search.txt_field:before{position:absolute;content:" ";width:1px;height:100%;background:#BDBDBD;left:0}
.navbar .custom_product_search .custom_select select{margin-right:15px;padding-right:10px}
.navbar .custom_dropdown .dropdown .dropdown-toggle{border-radius: 4px;border: 1px solid #EAEAEA;background: #FFF;box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);padding:8px 20px}
.navbar .custom_dropdown .dropdown a.dropdown-item:hover{background:var(--pink);color:white;border-radius: 2px}
.navbar .custom_dropdown .dropdown a.dropdown-item {padding:4px;font-family:"LeagueSpartan-Regular";font-size:15px;line-height:22px}
.navbar .custom_dropdown .dropdown a.dropdown-item  i{margin-right:8px;color:#8B909A}
.navbar .custom_dropdown .dropdown ul.dropdown-menu.show {padding:10px;border-radius: 4px;background: #FFF;box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);display:flex;flex-direction:column;gap:10px;max-height:330px;overflow-y: scroll}
.navbar .custom_dropdown .dropdown a.dropdown-item:hover i{color:white}
.navbar .custom_dropdown .dropdown .custom_icon_dropdown i{color:var(--pink);font-size:14px;margin-right:5px;position:relative}
.navbar .custom_dropdown .dropdown .custom_icon_dropdown i.caret_dropdown_icon{position:absolute;right:5px;top:12px;color:var(--text)}
.navbar .custom_dropdown.custom_simple_select_dropdown .dropdown .custom_icon_dropdown i.caret_dropdown_icon{top:2px;font-size:14px}
.navbar .custom_dropdown .dropdown .custom_icon_dropdown { min-width: 150px;height: 42px;border-radius: 4px;background-color: #fff;border: solid 1px #EAEAEA;text-align: left;padding: 10px 30px 10px 20px;color: var(--text);font-family:"LeagueSpartan-Regular";font-size:14px;box-shadow: 0px 4px 15px 0px rgba(0, 0, 0, 0.05);}
.navbar .custom_dropdown .dropdown  .dropdown-menu {width: 100%;padding: 0;margin: 0;border-top-left-radius: 0;border-top-right-radius: 0;}
.navbar .custom_dropdown .dropdown  .dropdown-menu .logout{color:#EF4444;}
.navbar .custom_product_search .custom_simple_select_dropdown .dropdown:has(.category_custom_select2) {position: relative;}
.navbar .custom_product_search .custom_simple_select_dropdown .category_custom_select2 {-webkit-appearance: none;-moz-appearance: none;appearance: none;padding-right: 30px;font-size: 16px;}
.navbar .custom_product_search .custom_simple_select_dropdown .category_custom_select2::after {content: '\f107';font-family: 'Font Awesome 6 Free'; font-weight: 900;position: absolute; top: 50%;right: 10px; transform: translateY(-50%);}
/*.navbar .custom_dropdown.website_dashboard_dropdown .dropdown .dropdown-toggle::after{display:none}*/
.navbar .website_dashboard_dropdown .dropdown .dropdown-toggle::after{display:none}
.website_dashboard_dropdown .user_details_img_wrap_top{width: 40px;height:40px;margin-right: 8px;border-radius: 50%;overflow: hidden}
.website_dashboard_dropdown .user_details_img_wrap_top img{width: 100%;height: 100%;object-fit: cover}
#kt_body .website_dashboard_dropdown .dropdown-toggle{border-radius: 10px; background: unset; padding: 0px;border: none;font-size: 16px;color: white;font-family: "LeagueSpartan-Regular";line-height: normal;height: unset;display: flex;align-items: center;}
#kt_body .website_dashboard_dropdown .dropdown-toggle i:nth-child(1){color:#9c2d5f;font-size:28px;font-weight:500}
#kt_body .website_dashboard_dropdown .dropdown-toggle i:nth-child(2){color:#000;margin:0px;}
.location_dropdown {border-radius: 4px;border: 1px solid #EAEAEA;background: #FFF;box-shadow: 0px 4px 8px 0px rgba(0, 0, 0, 0.05);padding:8px 20px}
.location_dropdown select{border:none;font-family:"LeagueSpartan-Regular";font-size:14px;color:var(--text)}
.location_dropdown i{color:var(--pink);font-weight:900;font-size:14px}
.header_icons {display:flex;align-items:center;gap:30px}
.header_icons .custom_flex i{color:var(--pink);font-size:14px;font-weight:900}
body .mega_menu_banner .accessories_select.category_custom_select2{padding-right:80px}
body .mega_menu_banner .accessories_select.category_custom_select2:after{    content: '\f107';
font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    right: 10px;
    transform: translateY(-50%);}

.mega_menu_banner  .dropdown .dropdown-toggle {border-radius: 10px;background: #C12A6D ;padding:15px 30px;border:none;font-size:16px;color:white;font-family:"LeagueSpartan-Regular";line-height:normal;height:52px}
.mega_menu_banner   .dropdown .dropdown-toggle i{color:var(--white);font-size:16px;margin-right:10px}
.mega_menu_banner .dropdown-menu.show {width:100%;padding:5px 20px;border-radius: 0px 0px 10px 10px;background: #FFF;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);max-height:460px;overflow-y: scroll}
.mega_menu_banner   .dropdown{width:fit-content}
.mega_menu_banner ul.dropdown-menu.show li a{padding:15px;font-size:16px;color:var(--text);font-family:"LeagueSpartan-Regular";line-height:normal}
/*.mega_menu_banner ul.dropdown-menu.show li:not(:last-child) a{border-bottom: 1px solid #E6E6E6;}*/
.mega_menu_banner .custom_mega_menu_list {display: flex;align-items: center;width: 100%}
.mega_menu_banner .mega_menu .mega_list {display: flex;list-style: none;justify-content: space-between;margin:0;flex-wrap: wrap;gap:20px}
/*.mega_menu_banner .mega_menu{width: 100%;overflow-x: auto;white-space: nowrap;}*/
.mega_menu_banner select{border:none;min-width:110px}
.mega_menu_banner .mega_menu .mega_list .nav_item .nav_link:has(i) {font-size:14px;color:var(--pink);font-family:"LeagueSpartan-semiBold";}
.mega_menu_banner .mega_menu .mega_list .nav_item .nav_link:has(i) i{color:var(--pink);font-size:14px;font-weight:900;margin-right:5px}
.mega_menu_banner .mega_menu .mega_list .nav_item .nav_link select{font-family:"LeagueSpartan-Regular";font-size:14px;color:var(--text);}
.mega_menu_banner .mega_menu .mega_list .nav_item .nav_link.more_view{color:var(--pink);font-family:"leagueSpartan-semiBold";font-size:14px}
.mega_menu_banner{padding:15px 0px 0px 0px}
/*.mega_menu_banner .custom_mega_menu_list .dropdown-toggle::after{display:none}*/
.mega_menu_banner .custom_mega_menu_list .dropdown-toggle{width:300px}
/*footer*/

/*chatbot*/
.chatbot_icon_toggle {animation: pulseScale 1.5s ease-in-out infinite;transition: transform 0.3s ease;}
@keyframes pulseScale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.15);
    }
    100% {
        transform: scale(1);
    }
}
.commision_off_wrapper {animation: pulseScale 1.5s ease-in-out infinite;transition: transform 0.3s ease;}
@keyframes pulseScale {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.19);
    }
    100% {
        transform: scale(1);
    }
}
.super_deals_slider_wrapper .single_product_wrapper:hover{transform: unset;}
.single_product_wrapper:hover{transform: scale(1.04);transition: transform 0.5s ease;}
.btn_orange:hover {background-color:transparent;border:1px solid #F76447;transition: all 0.7s ease;color: #F76447;}
.nav-link.fetch_product_by_category:hover{border:0px;}
.product_image img {transition: transform 0.5s cubic-bezier(0.68, -0.6, 0.32, 1.6);}
/*.product_image img:hover {transform: scale(1.1);}*/
.chatbot_container .chatbot_header .image_container{width:25px;height:25px}
.chatbot_container .chatbot_header .image_container img{width:100%;height:100%;object-fit:contain;border-radius:50%}
.chat_user {display:flex;align-items:center;gap:10px}
.chatbot_header {padding:15px 20px 20px 20px;background:var(--pink);display:flex;justify-content:space-between;align-items:center}
.chatbot_header i{font-size:18px;color:var(--white)}
.chatbot_container .chatbot_header .chat_user h5{color:var(--white)}
.chatbot_container .chatbot_footer{padding:20px;display: flex;align-items: center;gap:12px}
.chatbot_container button.send_msg_button {border:none;background:#9d9db422;padding:12px;border-radius:5px}
.chatbot_container button.send_msg_button i{color:var(--pink);font-size:15px}
.chatbot_container .chatbot_footer input{background:#9d9db422;width:100%}
.chat_body {padding: 15px;overflow-y: auto;flex-grow: 1;   scrollbar-width: none;max-height: 400px;}
.user_message {margin-left:auto ;background-color: var(--pink);color: var(--white);padding: 10px;margin-bottom: 10px;border-radius: 5px;max-width: 70%;align-self: flex-end;word-wrap: break-word;}
.user_message p{color:white}
.chatbot_footer input {background: #9d9db422;border-radius: 5px;padding: 8px;width: 80%;}
.chatbot_footer button.send_msg_button {border: none;background: #9d9db422;padding: 12px;border-radius: 5px;cursor: pointer;}
.chatbot_footer button.send_msg_button i {color: var(--pink);font-size: 15px;}
.chat_body::-webkit-scrollbar {display: none;}
.chatbot_message {margin-right: auto ;background-color: #9d9db422;color: var(--white);padding: 10px;margin-bottom: 10px;border-radius: 5px;max-width: 70%;align-self: flex-end;word-wrap: break-word;}
/*hero_slider*/
.hero_slider .hero_slider_txt_content .slider_headphone_image {height: 435px;}
.hero_slider .hero_slider_txt_content .slider_headphone_image img {width: 100%;height: 100%;object-fit: contain;}
.hero_slider{background-image:url("/website/assets/images/frame_bg.png");background-size: cover;background-repeat: no-repeat;}
.hero_slider .hero_swiper  .hero_slider_txt_content h1 span{font-size:68px;font-family:"leagueSpartan-Light";font-weight:200;letter-spacing:-1.36px;display: block;}
.hero_slider .hero_swiper  .hero_slider_txt_content{padding: 40px 0px;text-align: center}
.hero_slider .hero_swiper .hero_slider_txt_content   h1{font-weight:200;text-transform:uppercase;}
.hero_slider .hero_swiper .hero_slider_txt_content  h4{font-weight:300}
.hero_slider .hero_swiper .hero_slider_txt_content  .product_price{margin:25px 0px}
.hero_slider .hero_swiper .hero_slider_txt_content  .product_price h2{font-size:52px;font-family:"leagueSpartan-SemiBold";margin-top:10px}
.hero_slider .hero_swiper  .hero_slider_txt_content .product_price .slider_btn .btn_main.btn_pink{display:flex;align-items:center;justify-content:center;margin-top:25px;width:fit-content}
.hero_slider .hero_slider_txt_content p{text-transform: uppercase}
.hero_slider .hero_slider_txt_content .slider_btn:has(img) img{margin-left:15px}
.hero_slider .hero_swiper .hero_slider_txt_content.crystal_clear_txt{padding-left: 20px;text-align: start}
span.swiper-pagination-bullet.swiper-pagination-bullet-active {width:30px;border-radius:5px;background:var(--pink)}
.hero_slider .swiper-pagination{left: -19%;bottom:30px}

.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--selected,
.select2-container--bootstrap5 .select2-dropdown .select2-results__option.custom-class.select2-results__option--selected {background-color: #C12A6D ;color: white ;}
.select2-container--bootstrap5 .select2-dropdown .select2-results__option.select2-results__option--highlighted:hover,
.select2-container--bootstrap5 .select2-dropdown .select2-results__option.custom-class.select2-results__option--highlighted:hover {background-color: #C12A6D ;color: white ;}

.catch_big_products {border-radius: 10px;border: 1px solid #F2F2F2;background: #F2F5F8;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);padding:20px 20px 0px 20px}
.catch_big_products h4, .catch_big_products h2{text-transform:uppercase;font-weight:300}
.catch_big .custom_row{row-gap:20px}
.login_input_wrapper.input_wrapper:has(.is-valid) i{right: 10%;top: 13px;}
.catch_big_product_image {width:300px;height:240px;}
.catch_big_product_image img{width:100%;height:100%;object-fit:contain;object-position: bottom}
.catch_big{margin:40px 0px 80px 0px}
a.shop_now {font-size:16px;color:var(--text);padding-bottom:20px;display: flex;align-items:center;}
a.shop_now:has(i) i{margin-left:15px;color:#F76447;font-size:20px;font-weight:900}
.catch_big .catch_big_products .custom_justify{align-items: end;margin-top:10px}
/*.single_pro_color_swiper2 .swiper-slide {width: auto !important;}*/
/*.single_pro_color_swiper2 .swiper-wrapper{column-gap:20px}*/
/*saad*/
/*super deals sec*/
.ratings_star_wrapper p{color:#5858ab;font-weight:600;}
.product_sale_sec {padding-bottom:80px;}
.product_sale_sec .super_deals_slider_wrapper .swiper-slide.swiper-slide-next {z-index:-1}
.timer_heading_wrapper {display:flex;align-items:center;column-gap:20px;}
.timer_heading_wrapper .end_sale_time_wrapper{display:flex;align-items:baseline;column-gap:10px;border-radius: 144px;background: rgba(247, 100, 71, 0.20);padding: 10px 15px;}
.timer_heading_wrapper .end_sale_time_wrapper i{color: #F76447;font-size: 16px;}
.timer_heading_wrapper .end_sale_time_wrapper label{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 16px;}
.timer_heading_wrapper .end_sale_time_wrapper .timer-wrapper span{color: #3E3E3E;font-family: 'leagueSpartan-SemiBold';font-size: 16px;}
.product_image img{width:100%;height:100%;object-fit:contain;}
.product_img_saleoff_wrapper div:has(.product_image){padding:50px 0px;}
.product_image{width: 200px;height: 200px;}
.product_sale_sec .super_deals_slider_wrapper .swiper-slide{height:auto;}
.single_product_wrapper {justify-content:space-between;height:100%;border-radius: 10px;border: 2px solid #F2F2F2;background: #FFF;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);padding: 20px;display: flex;  flex-direction: column;}
.product_details_wrapper {display:flex;flex-direction:column;row-gap:10px;}
.product_details_wrapper .ratings_star_wrapper{display:flex;align-items:center;column-gap:5px;}
.product_details_wrapper .ratings_star_wrapper div span{color: #C12A6D;font-size: 14px;}
.product_details_wrapper .product_category_name_wrapper p span{color: #C12A6D;font-weight: 600;padding-left:5px;}
.single_product_wrapper .price_addtocart_wrapper{display:flex;align-items:center;justify-content:space-between;}
.single_product_wrapper .price_addtocart_wrapper h5{color: #C12A6D;text-align: end;}
.single_product_wrapper .price_addtocart_wrapper h5 span{color: #3E3E3E;font-size: 14px;font-weight: 400;padding-left:5px;text-decoration-line: line-through;display: block;}
.product_img_saleoff_wrapper {display:flex;align-items: start;position: relative;justify-content: space-between;}
.commision_off_wrapper {background-image: url('/website/assets/images/product_commision_back_img.svg');background-size: cover;background-position:center;width: 70px;height: 70px;background-repeat:no-repeat;display:flex;align-items:center;justify-content:center;column-gap: 5px;}
.commision_off_wrapper h4{color: #FFF;}
.commision_off_wrapper div {display:flex;flex-direction:column;}
.commision_off_wrapper div label{color: #FFF;font-size: 17px;font-weight: 600;line-height: 0.7;}
.commision_off_wrapper div span{color: #FFF;font-size: 8px;font-weight: 300;}
/*black headphone sec*/
/*.headphone_sec{background-size: cover;background-image: url('/website/assets/images/headphone_black.png');background-position:center;background-repeat:no-repeat;padding:80px 0px;}*/
.headphone_sec{background-color:#363434;}
.headphone_sec .headphone_wrapper h4,.headphone_sec .headphone_wrapper h2,.headphone_sec .headphone_wrapper p{color:#fff;}
.headphone_sec .headphone_wrapper h4:nth-child(3){margin-top:20px;}
.headphone_sec .headphone_wrapper div{padding:15px 0px;}
.headphone_sec_img img{width:100%;height:100%;object-fit:cover;border-radius:20px; object-position: top}
.headphone_sec_img {height: 350px;padding: 30px 0px;}
.headphone_sec .headphone_wrapper {padding: 30px 0px;min-height: 100%;display: flex;flex-direction: column;justify-content: center;}
/**/
/*popular products*/
.offcanvas-header.your_cart{justify-content:space-between}
.popular_products_wrapper .all_products_show li{list-style:none;margin: 0;}
.popular_products_wrapper .all_products_show {margin:0;padding:0}
.popular_products_wrapper h3{white-space: nowrap}
.popular_products_wrapper.products_list_menu{gap:15px}
.product_image_wrapper {display:flex;justify-content:center;align-items:center;padding: 10px 0px;}
.popular_products_wrapper {display:flex;justify-content:space-between;padding-bottom: 20px;overflow:hidden;gap:40px}
.popular_products_wrapper .popular_products_wrapper ul{column-gap:30px;border:0px;}
.popular_products_wrapper .popular_products_wrapper ul li button{padding:0px;color: #7C7C7C;font-family: 'leagueSpartan-Medium';font-size: 16px;font-weight: 500;}
.popular_products_wrapper .popular_products_wrapper ul li button.active{color: #3E3E3E;font-weight: 700;border:0px;border-bottom: 1px solid #3E3E3E;padding-bottom: 5px;}
.popular_products_sec{padding:80px 0px;}
.popular_products_sec .popular_products_wrapper .nav-tabs { display: flex; flex-wrap: nowrap;overflow-x: auto; -webkit-overflow-scrolling: touch; scrollbar-width: auto; scrollbar-color: auto;}
.popular_products_sec .popular_products_wrapper .nav-tabs .nav-item {flex: 0 0 auto;margin-right: 15px;}
.popular_products_sec .popular_products_wrapper .nav-tabs::-webkit-scrollbar {width: 0px;}
.popular_products_sec .popular_products_wrapper .nav-tabs::-webkit-scrollbar-track {background: #f1f1f1;  }
.popular_products_sec .popular_products_wrapper .nav-tabs:hover {scrollbar-width: auto; scrollbar-color: auto; }
.popular_products_sec .popular_products_wrapper .nav-tabs::-webkit-scrollbar{height:3px}
.popular_products_sec .popular_products_wrapper .nav-tabs::-webkit-scrollbar:hover{height:6px}
/**/
/*product category page */
.product_filter_wrapper{max-width: 81%;}
.product_categories_wrapper {display:flex;flex-direction:column;row-gap:20px;padding-top:85px;}
.commision_color_var_wrapper {display:flex;justify-content: end;width: 100%;}
.commision_color_var_wrapper .color_variation_wrapper i{color:#000;font-size:17px;}
.commision_color_var_wrapper .color_variation_wrapper{display:flex;column-gap:3px;align-items:center;}
/*filter sec css*/
#kt_body .size_filter_wrapper input[type="checkbox"]:checked+label{color:#fff;}
#kt_body .radio_size_filter input[type="radio"]:checked+label{color:#fff;}
#kt_body .size_filter_wrapper.size_filter_wrapper_radio input[type="radio"]:checked+label{color:#fff;}
.size_filter_wrapper .accordion-body{padding:0px;row-gap:20px;display:flex;flex-direction:column;}
.size_filter_wrapper .size_waist_wrapper .waist_number_wrapper{display:flex;gap:8px 4px;justify-content: flex-start;  flex-wrap: wrap;}
.size_filter_wrapper .size_waist_wrapper{display:flex;flex-direction:column;row-gap:8px}
.category_filter_wrapper .accordion-body {padding:20px 0;display:flex;flex-direction:column;row-gap:5px;}
.category_filter_wrapper .accordion-body .form-group{display:flex;column-gap:8px;align-items:center;}
.category_filter_wrapper .accordion-body .form-group label{max-width:60%;font-size: 12px;line-height: 16px;letter-spacing: 0.2px;color: #000;}
.category_filter_wrapper .accordion-body .form-group input[type="checkbox"]{ width: 20px;height: 32px;border: 0.5px solid #000;accent-color: #C12A6D;}
/*.category_filter_wrapper input[type=checkbox] {transform: scale(1.5);}*/
.product_categories_sec {padding:60px 0px 150px 0px;}
.breadcrump_wrapper .breadcrumb .breadcrumb-item a{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 14px;font-weight: 400;line-height: 21px;opacity: 0.5;}
.breadcrump_wrapper .breadcrumb .breadcrumb-item.active{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 14px;font-weight: 400;line-height: 21px;}
.breadcrump_wrapper{padding-bottom:35px;}
.breadcrumb .breadcrumb-item:after{opacity: 0.5;}
.product_filter_wrapper .total_products_count{display: flex;padding: 16px 0px;border-bottom: 1px solid  #DDDBDC;}
.product_filter_wrapper  .accordion-button.collapsed,.product_filter_wrapper  .accordion-button{padding: 16px 0px;color: #262626;font-size: 14px;font-weight: 600;line-height: 21px;letter-spacing: 0.42px;}
.product_filter_wrapper  .accordion-button{background:transparent;}
.product_filter_wrapper .category_filter_wrapper,.product_filter_wrapper .color_filter_wrapper{border-bottom: 1px solid  #DDDBDC;}
.color_filter_wrapper .accordion-body{display:fleex;justify-content:center;padding: 40px 20px 20px 20px;flex-wrap: wrap;row-gap: 40px;  column-gap: 37px;}
.color_filter_wrapper  .form-group{display:flex;flex-direction:column;justify-content:center;row-gap:8px;}
.color_filter_wrapper .form-group label{font-family: 'leagueSpartan-Regular';font-size: 12px;line-height: 16px;letter-spacing: 0.2px;color: #000;padding-top: 10px;}
.accordion-button:not(.collapsed) {box-shadow: none;}
.color_filter_wrapper .accordion-body:has(.color_filter_wrapper.new_colors) {padding:0px 0px 30px 0px}

.category_filter_wrapper .accordion-body,.color_filter_wrapper .accordion-body{overflow-y: scroll;max-height:170px;}
.category_filter_wrapper .accordion-body::-webkit-scrollbar,.color_filter_wrapper .accordion-body::-webkit-scrollbar {  display: none;  }
.category_filter_wrapper .accordion-body,.color_filter_wrapper .accordion-body {  -ms-overflow-style: none;  scrollbar-width: none;  }
.color_filter_wrapper.new_colors{border:0;height:100px;
    overflow-y:scroll;}
.color_filter_wrapper.new_colors .color_filter_grid.select_classic_shirts_colors {row-gap:40px}
.color_filter_wrapper.new_colors .color_filter_grid{column-gap: 60px;display: flex;flex-wrap: wrap;row-gap: 28px;max-width:90%}
.color_filter_wrapper  .form-group{position:relative;}
.color_filter_wrapper .checkbox {overflow: visible;}
.color_filter_wrapper .checkbox__input {display: none;}
.color_filter_wrapper .checkbox__inner {  display: inline-block;  width: 24px;  height: 24px;  border-radius: 50%;  background: #000 no-repeat center;  position: absolute;  bottom: 100%;  right: 0;  left: 0;  margin: auto;  }
.color_filter_wrapper .checkbox__input:checked + .checkbox__inner {  background-image: url("data:image/svg+xml,%3C%3Fxml version='1.0' encoding='UTF-8'%3F%3E%3Csvg width='14px' height='10px' viewBox='0 0 14 10' version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink'%3E%3C!-- Generator: Sketch 59.1 (86144) - https://sketch.com --%3E%3Ctitle%3Echeck%3C/title%3E%3Cdesc%3ECreated with Sketch.%3C/desc%3E%3Cg id='Page-1' stroke='none' stroke-width='1' fill='none' fill-rule='evenodd'%3E%3Cg id='ios_modification' transform='translate(-27.000000, -191.000000)' fill='%23FFFFFF' fill-rule='nonzero'%3E%3Cg id='Group-Copy' transform='translate(0.000000, 164.000000)'%3E%3Cg id='ic-check-18px' transform='translate(25.000000, 23.000000)'%3E%3Cpolygon id='check' points='6.61 11.89 3.5 8.78 2.44 9.84 6.61 14 15.56 5.05 14.5 4'%3E%3C/polygon%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/g%3E%3C/svg%3E");  background-size: 14px 10px;  }
#kt_body .radio_size_filter input[type="radio"] {padding: 15px 28px;appearance: none;background-color: #F5F4F4;-webkit-appearance: none;cursor: pointer;border-radius: 4px}
#kt_body .radio_size_filter input[type="radio"]::before {border-radius: 4px;content: "";padding: 15px 28px;background-color: #C12A6D;position: absolute;left: 50%;top: 50%;transform: translate(-50%, -50%);display: none;}
#kt_body .radio_size_filter input[type="radio"]:checked::before {display: block;}
.size_filter_wrapper .members>div {  position: relative;  display: flex;  align-items: center;  justify-content: center;  }
.size_filter_wrapper .members>div label {  position: absolute;  user-select: none;  pointer-events: none;  z-index: 10;  }
#kt_body .size_filter_wrapper input[type="checkbox"] {  padding: 15px 28px;  appearance: none;  background-color: #F5F4F4;  -webkit-appearance: none;  cursor: pointer;  }
#kt_body .size_filter_wrapper input[type="checkbox"]::before {  content: "";  padding: 15px 28px;  background-color: #C12A6D;  position: absolute;  left: 50%;  top: 50%;  transform: translate(-50%, -50%);  display: none;  }
#kt_body .size_filter_wrapper input[type="checkbox"]:checked::before {display: block;}
#kt_body .size_filter_wrapper.size_filter_wrapper_radio input[type="radio"]:checked::before {display: block;}
/*My Dashboard*/
.dataTables_empty{text-align:center;}
.dashboard_cards {padding:24px;border-radius: 10px;background: var(--white);margin-top:30px}
.my_dashboard, .active_orders , .account_settings{background: #FAFAFA;padding:30px 0px 100px }
.dashboard_cards p{font-size:16px;color:#8B909A;margin-top:10px}
.dashboard_cards .card_orders_status h3{font-family: 'leagueSpartan-Bold';margin: 30px 0px 5px 0px;}
.dashboard_cards .card_orders_status p span{color:#6C3DBD;font-size:16px;font-family:"LeagueSpartan-Regular";line-height:20px;margin-right:8px}
.dashboard_cards .card_orders_status p span i{color:#6C3DBD;font-size:13px;font-weight:900;}
.dashboard_cards .card_orders_status p span.low_value, .dashboard_cards .card_orders_status p span.low_value i{color:#D02626}
.custom_table {border-radius: 10px;background: var(--white);padding:8px 0px;margin-top:20px}
.custom_table .table_wrapper table tr th:first-child, .custom_table .table_wrapper table tr th:last-child{padding:16px 36px}
.custom_table .table_wrapper table tr th{border-bottom: 1px solid #E9E7FD;padding:16px 20px ;color:#8B909A;font-size:14px;font-family:"leagueSpartan-Medium"}
.custom_table .table_wrapper table tr td{padding:20px;font-family: 'PublicSans-Regular';font-size:15px;color:#23272e;line-height:22px;border-bottom: 1px solid #E9E7FD;}
.custom_table .table_wrapper table tr td:first-child, .custom_table .table_wrapper table tr td:last-child{padding: 20px 36px}
.custom_table .table_wrapper table  .table_user_name_wrapper .table_user_img {width:30px;height:30px}
.custom_table .table_wrapper table  .table_user_name_wrapper  .table_user_img img{width:100%;height:100%}
.custom_table .table_wrapper table  td a.table_view_detail {color:#C12A6D;font-family:"PublicSans-Regular";}
.back_btn_view_details{display:flex;flex-direction:column;row-gap:10px;}
.back_btn_view_details a{width:fit-content;}
.back_btn_view_details a i{margin:0px;color:#fff;font-weight:800;font-size:14px;}
.custom_table .table_wrapper table tr td .table_user_name_wrapper  p{font-family: 'PublicSans-Regular';font-size:15px;color:#23272e;line-height:22px;}
.active_orders .heading{margin-bottom:30px}
.active_orders .custom_search .txt_field:has(input[type="number"]) {position:relative}
.active_orders .custom_search .txt_field:has(input[type="number"]) input,.custom_filter .txt_field:has(.custom_select_filter) select{border-radius: 4px;background:var(--white);box-shadow: 0px 4px 16px 0px rgba(0, 0, 0, 0.04);border:none;padding:8px 25px 8px 16px;height:40px}
.active_orders .custom_search .txt_field .search_icon_svg{position:absolute;right:15px;top:25%;}
.custom_table div#DataTables_Table_0_length {margin-left:35px}
.custom_table select.form-select.form-select-sm.form-select-solid {background:none;padding:10px 20px;border-radius: 6px;border: 1px solid #E9E7FD;}
.custom_table .dataTables_paginate {margin-right:35px}
.txt_field{position:relative}
.custom_table .dataTables_paginate  .pagination .paginate_button .page-link{border-radius: 4px;background: #F1F2F6;padding:4px 8px;color:#8B909A;font-size:13px;font-family:"LeagueSpartan-Medium";line-height:20px}
.custom_table .dataTables_paginate  .pagination .paginate_button.active .page-link{background:#c12a6D;color:white;box-shadow: 0px 2px 4px 0px rgba(165, 163, 174, 0.30);}
.custom_table .custom_search .txt_field:has(input[type="number"]) input::placeholder, .active_orders .custom_filter .txt_field:has(.custom_select_filter) select{color:#8B909A;font-family:"LeagueSpartan-Regular";line-height:21px}
/*account_settings*/
.account_settings .profile_settings_wrapper .custom_row{row-gap:33px}
.account_settings .profile_settings_wrapper {border-radius: 10px;background: var(--white);padding:20px}
.account_settings .profile_settings_wrapper .image-input.image-input-circle {width:93px;height:93px}
.account_settings .profile_settings_wrapper .image-input.image-input-circle img{width:100%;height:100%;border-radius:50%;object-fit:cover}
.account_settings .profile_settings_wrapper.passwords_field{margin-top:28px}
.account_settings .profile_settings_wrapper .btn_main.btn_pink{border:none}
.account_settings .profile_settings_wrapper .txt_field .input_icon {position:absolute;right:10px;top:62%}
.account_settings .profile_settings_wrapper  .file_edit_icon{background:var(--pink);}
.account_settings .profile_settings_wrapper  .file_edit_icon i{color:var(--white);}
.account_settings h3{margin-bottom:27px}
/*classic_black_grey_stripe_shirt*/
.product_image_wrapper.product_image_wrapper_product_details {padding:20px;}
.classic_black_grey_stripe_shirt .custom_row{row-gap:20px}
.classic_shirts_images img{border: 1px solid #F2F2F2;background: #FFF;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);}
.classic_black_grey_stripe_shirt .product_images_gird{width:100%;display:grid;grid-template-columns: 48% 48%;grid-row: auto auto;grid-column-gap: 10px;grid-row-gap: 20px;}
.classic_black_grey_stripe_shirt .images_row{row-gap:8px}
.classic_black_grey_stripe_shirt {padding:60px 0px 100px 0px}
.classic_black_grey_stripe_shirt .classic_shirts_images {height:500px}
.classic_black_grey_stripe_shirt .classic_shirts_images img{width:100%;height:100%;border-radius:10px;object-fit:contain;
}
.classic_black_grey_stripe_shirt .classic_black_shirts .ratings span {color:#000;opacity:0.5}
.classic_black_grey_stripe_shirt .classic_black_shirts .ratings span.checked{color:#FFAD33;opacity:1}
.classic_black_grey_stripe_shirt .classic_black_shirts .products_instock {display: flex;align-items: center;gap: 16px;margin:20px 0px}
.classic_black_grey_stripe_shirt .classic_black_shirts .products_instock p {border-left: 1px solid #000;height: 100%;padding-left: 16px;color: #47A400;font-family: "Poppins-Regular";}
.classic_black_grey_stripe_shirt .classic_black_shirts .classic_shirt_price{font-family:"leagueSpartan-Bold"}
.classic_black_grey_stripe_shirt .classic_black_shirts .classic_shirts_colors{display:flex;align-items:center;gap:12px;margin:30px 0px 12px}
.classic_black_grey_stripe_shirt .classic_black_shirts .custom_justify a{text-decoration:underline;color:#000}
.classic_black_grey_stripe_shirt .classic_black_shirts .btn_main.btn_pink{border:none;width:100%;}
.classic_black_grey_stripe_shirt .classic_black_shirts .add_tag_btn{padding:40px 0px 30px;}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description table tbody tr td {padding: 12px 10px;}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .custom_flex {gap:16px;align-items:start;border-top:1px solid #F5F4F4;padding:15px 0px }
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .custom_flex .image_container img{width:100%;height:100%;object-fit:contain}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .custom_flex .image_container{width:35px;height:35px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description {padding:10px 0px 0px 0px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .custom_flex p.desc_para{font-family:"LeagueSpartan-Regular";line-height:16px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .custom_flex p{font-size:15px;font-family:"LeagueSpartan-Medium";line-height:21px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .part_shirt_heading{padding:40px 0px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .part_shirt_heading h6{margin-bottom:20px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .part_shirt_heading p{line-height:17.8px;letter-spacing:1.4px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc  .custom_flex h6{margin-right:50px;min-width:100px}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .custom_flex{border-top:none ;border-bottom:1px solid #F5F4F4}
#kt_body .classic_black_grey_stripe_shirt .classic_black_shirts  .waist_number_wrapper.members {display:flex;gap:15px;margin-top: 15px}
#kt_body .classic_black_grey_stripe_shirt .classic_black_shirts  .size_filter_wrapper input[type="checkbox"]{padding:20px;border-radius:5px}
#kt_body .classic_black_grey_stripe_shirt .classic_black_shirts  .size_filter_wrapper input[type="checkbox"]::before{padding:20px;border-radius:5px;}
.terms_conditions .box_shadow_wrapper h2{margin-bottom:20px}
.terms_conditions{background:white;margin: 50px 0px}
.terms_conditions .box_shadow_wrapper  .terms_conditions_points h6{color:#000;font-weight:500;margin-bottom:20px}
.terms_conditions .box_shadow_wrapper h2{margin-bottom:20px}
.terms_conditions .box_shadow_wrapper  .terms_conditions_points ol{display:flex;flex-direction:column;gap:10px}
/*Color filter without labels*/
.classic_black_grey_stripe_shirt .round_color {  position: relative;  }
.select_classic_shirts_colors {  display: flex;  gap: 20px;  margin: 15px 0px 20px;  align-items: center;  }
.round_color label {  background-color: #fff;  border: 1px solid #ccc;  border-radius: 50%;  cursor: pointer;  height: 28px;  width: 28px;  position: absolute;  top: 0;  left: 0;  }
/*.round_color label:after {  content: "";  border: 2px solid #fff;  border-top: none;  border-right: none;  height: 6px;  width: 12px;  position: absolute;  top: 8px;  left: 7px;  transform: rotate(-45deg);  opacity: 0;  }*/
.round_color label:after {  content: "";  border: 2px solid var(--tick-color, white); /* Default tick color is white */  border-top: none;  border-right: none;  height: 6px;  width: 12px;  position: absolute;  top: 8px;  left: 7px;  transform: rotate(-45deg);  opacity: 0;  transition: border-color 0.3s;  }
.round_color input[type="radio"]:checked + label:after {
    opacity: 1; /* Show the tick */
}
.round_color input[type="checkbox"]:checked + label:after {
    opacity: 1; /* Show the tick */
}

.round_color input[type="radio"] {  visibility: hidden; /* Hides the radio input */  }
.round_color input[type="radio"]:checked + label:after {  opacity: 1; /* Shows the checkmark when a radio button is selected */  }
.super_deals_slider_wrapper.you_may_like_this h3 {  margin: 40px 0px 20px;  }
/*wishlist_page*/
.wishlist_page{padding:50px 0px}
.wishlist_page .custom_row{row-gap:20px}
/*off canvas modal css*/
.offcanvas-backdrop.show {opacity: unset;background: rgba(0, 0, 0, 0.60);}
.modal-body_wrapper {display:flex;flex-direction:column;row-gap:16px;}
.modal-body_wrapper h4{font-weight: 600;font-family: 'leagueSpartan-SemiBold';line-height: 24px;letter-spacing: 0.72px;}
.product_add_to_cart_single_wrapper{display:flex;column-gap:16px;align-items: center;}
.product_add_to_cart_single_wrapper .modal_single_img_wrapper div img{width:100%;height:100%;object-fit:cover;}
.product_add_to_cart_single_wrapper .modal_single_img_wrapper div{width: 75px;height: 60px;}
.product_add_to_cart_single_wrapper .modal_prod_details_trash_wrapper .modal_product_name_clr{width:60%}


.product_add_to_cart_single_wrapper .modal_single_img_wrapper{border-radius: 4px;border: 1px solid #F2F2F2;padding: 20px 12px 20px 13px;display:flex;align-items:center;justify-content:center;}
.product_add_to_cart_single_wrapper .modal_prod_details_trash_wrapper{display:flex;align-items:center;justify-content:space-between;}
.product_add_to_cart_single_wrapper .modal_prod_details_trash_wrapper .modal_product_name_clr p:nth-child(1){letter-spacing: 1.4px;font-family: 'leagueSpartan-Medium';}
 .product_add_to_cart_single_wrapper .modal_prod_details_trash_wrapper .modal_product_name_clr p:nth-child(2){font-size: 12px;letter-spacing: 1.4px;opacity: 0.5;margin-top:2px;}
 .product_add_to_cart_single_wrapper .modal_prod_details_trash_wrapper .single_product_trash button{border:0px;background-color:transparent;}
 .product_add_to_cart_single_wrapper .modal_product_price_plus_wrapper{display:flex;justify-content:space-between;align-items:center;column-gap:50px}
 .product_add_to_cart_single_wrapper .modal_product_price_plus_wrapper div:has(h5){display:flex;align-items:end;column-gap:5px;}
 .product_add_to_cart_single_wrapper .modal_product_price_plus_wrapper div h5{color: #C12A6D;}
 .product_add_to_cart_single_wrapper .modal_product_price_plus_wrapper div p{text-decoration-line: line-through;}
 .product_add_to_cart_single_wrapper .input_plus_minus_wrapper{display:flex;border-radius: 4px;border: 1px solid #F2F2F2;padding: 12px;align-items:center;justify-content:center;column-gap:15px;}
 .product_add_to_cart_single_wrapper .input_plus_minus_wrapper input{width:50%;border:0px;text-align: center;}
.product_add_to_cart_single_wrapper .d-flex.size_and_color_filter { justify-content: space-between; align-items: center;}
.product_add_to_cart_single_wrapper .modal_single_details_wrapper { display: flex; flex-direction: column; gap: 10px;width:100%}
.product_add_to_cart_single_wrapper { border-bottom: 1px solid #f2f2f2; padding-bottom: 10px;}
.modal_bottom_continue_wrapper {display:flex;flex-direction:column;row-gap:32px;}
.modal_bottom_continue_wrapper .subtotal_wrapper{display:flex;justify-content:space-between;align-items:center;}
.modal_bottom_continue_wrapper .subtotal_wrapper div span{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 13px;font-weight: 400;line-height: 16.8px;letter-spacing: 1.4px;}
.modal_bottom_continue_wrapper .subtotal_wrapper div label{color: #3E3E3E;font-family: 'leagueSpartan-Medium';font-size: 14px;font-weight: 500;line-height: 16.8px; letter-spacing: 1.4px;}
.modal_bottom_continue_wrapper div p{text-align:center;}
.offcanvas-body{justify-content:space-between;display:flex;flex-direction:column;}
.offcanvas-header{justify-content:end;}
/**/
/*chackout page*/
.yout_cart_wrapper .single_chackout_order_wrap.product_add_to_cart_single_wrapper .modal_single_details_wrapper .modal_prod_details_trash_wrapper .single_product_trash h6{min-width:38%;word-break: break-word}
.yout_cart_wrapper .single_chackout_order_wrap.product_add_to_cart_single_wrapper .modal_single_details_wrapper .modal_prod_details_trash_wrapper .single_product_trash{display:flex;gap:3px;width:29%;align-items: center}
.product_add_to_cart_single_wrapper.single_chackout_order_wrap .modal_prod_details_trash_wrapper .modal_product_name_clr p:nth-child(1){max-width:97%;}
.product_add_to_cart_single_wrapper.single_chackout_order_wrap  .modal_single_img_wrapper div {width: 52px;height: 42px;}
.product_add_to_cart_single_wrapper.single_chackout_order_wrap .modal_single_img_wrapper{padding: 12px 7px;}
.place_order_sec{padding:60px 0px 100px 0px;position: relative}
.place_order_sec .billing_details_wrapper{display:flex;flex-direction:column;row-gap:20px;}
.place_order_sec .billing_details_form_wrapper{display:flex;flex-direction:column;row-gap:30px;}
.place_order_sec .billing_details_form_wrapper .form-group{display:flex;flex-direction:column;row-gap:8px;}
.place_order_sec .billing_details_form_wrapper .form-group label{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 16px;font-weight: 400;opacity: 0.5;}
.place_order_sec .billing_details_form_wrapper .form-group input{border-radius: 4px;border: 1px solid #F2F2F2;padding:15px;}
.place_order_sec .billing_details_form_wrapper .save_this_information_wrap{display:flex;align-items:center;column-gap:16px;}
.place_order_sec .billing_details_form_wrapper .save_this_information_wrap label{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 16px;font-weight: 400;opacity: 0.5; }
.place_order_sec .billing_details_form_wrapper .save_this_information_wrap input{accent-color: #C12A6D;width: 24px;height: 24px;}
.place_order_sec .yout_cart_wrapper {display:flex;flex-direction:column;row-gap:20px;padding-top: 60px}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper{display:flex;justify-content:space-between;align-items:center;padding-bottom:16px;border-bottom:1px solid #F2F2F2}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper_bank{display:flex;justify-content:space-between;align-items:center;}
.place_order_sec .yout_cart_wrapper  .checkout_trolley {width: 100px;height: 100px;position: absolute;left: -100px;right: 0;margin: auto;top: 45px;}
.place_order_sec .yout_cart_wrapper  .checkout_trolley img {width: 100%;height: 100%;object-fit: contain;transform: scaleX(-1);}

.place_order_sec .yout_cart_wrapper .subtotal_wrapper_bank .bank_cards_img_wrapper div img{width:100%;height:100%;object-fit:contain;}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper_bank .bank_cards_img_wrapper div{width: 37px;height: 21px;}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper_bank .bank_cards_img_wrapper{display:flex;column-gap:8px;}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper_bank .bank_chackbox_wrapper{display:flex;align-items:center;column-gap:16px;}
.yout_cart_wrapper .subtotal_wrapper_bank .bank_chackbox_wrapper label{color: #3E3E3E;font-family: 'Poppins-Regular';font-size: 16px;font-weight: 400;line-height: 24px;}
.yout_cart_wrapper .subtotal_wrapper_bank .bank_chackbox_wrapper input{width: 24px;height: 24px;accent-color:  #3E3E3E;}
.yout_cart_wrapper  .subtotal_wrapper_bank.coupon_code_wrappper input{border-radius: 4px;border: 1px solid #F2F2F2;padding: 16px 24px;width:100%;max-width:100%}
.yout_cart_wrapper  .subtotal_wrapper_bank.coupon_code_wrappper button{border-radius: 4px;border: 1px solid #C12A6D;background: #FFF;padding: 16px 24px;width:50%;max-width:100%}
.yout_cart_wrapper  .subtotal_wrapper_bank.coupon_code_wrappper{column-gap:10px;padding-top:10px;}
.yout_cart_wrapper .checkout_place_order_wrap button{width:100%}
.yout_cart_wrapper .checkout_place_order_wrap{padding-top:10px;}
.place_order_sec .yout_cart_wrapper .subtotal_wrapper.subtotal_wrapper-total_wrap{border:0px;}

/*login page css*/
.login_navbar_header .custom_nav select.form-select { --bs-form-select-bg-img:none;}
.login_navbar_header .custom_nav select.form-select{-webkit-appearance: menulist; }
.login_navbar_header .custom_nav select option{color:#000}
.navbar.login_navbar_header {background: #611DB7;background: linear-gradient(90deg, rgba(97, 29, 183, 1) 0%, rgba(226, 19, 73, 1) 100%);}
.login_navbar_header .login_logo_wrapper div img{width:100%;height:100%;object-fit:contain;}
.login_navbar_header .login_logo_wrapper div{width:140px;height:50px}
.login_navbar_header .custom_nav{display:flex;justify-content:space-between;align-items:center;}
.login_navbar_header .custom_nav select{padding:0px 0px;background-color:transparent;border:0px;color:#fff;}
.login_footer h6{text-align:center;color:#fff}
.login_footer{padding:8px;background: #611DB7;background: linear-gradient(90deg, rgba(97, 29, 183, 1) 0%, rgba(226, 19, 73, 1) 100%);}
.login_sec .login_fileds_wrapper  button.btn.btn_pink.back_btn i{color:white;font-size:14px}
.login_sec .login_fileds_wrapper button.btn.btn_pink.back_btn{width:fit-content}
.login_sec .login_fileds_wrapper button.btn.btn_pink.back_btn:hover{background:var(--pink)}
.login_sec .login_fileds_wrapper_inner{display: flex;  flex-direction: column;  row-gap: 17px;border-radius: 10px;  background: linear-gradient(180deg, rgba(255, 255, 255, 0.32) 0%, rgba(255, 255, 255, 0.80) 40%);  backdrop-filter: blur(2px);  padding: 50px 40px;}
.login_sec .login_logo_wrapper{padding-top:30px;}
.login_sec .custom_row_login{height: calc(100vh - 100px);}
.login_sec .login_fileds_wrapper{display:flex;flex-direction:column;row-gap:25px;justify-content:center;max-width:40%;width: 100%;margin: auto;}
.login_sec .login_pg_img_wrapper img{width:100%;height:600px;object-fit:cover;min-height: 100%}
.login_sec .login_details_wrapper{display:flex;flex-direction:column;row-gap:25px;align-items: center;height: calc(100vh - 100px);}
.login_sec .login_pg_img_wrapper{height:100%;}
.login_details_wrapper .login_logo_wrapper div img{width:100%;height:100%;object-fit:contain;}
.login_details_wrapper .login_logo_wrapper div{height: 58px;width: 200px;margin: auto;}
.login_details_wrapper .log_in_to_execlusive{display:flex;flex-direction:column;row-gap:18px;align-items: start; }
#kt_body .login_details_wrapper .login_input_wrapper input{border:0px;border-bottom:1px solid #c1bdbd;background-color:transparent;border-radius:0px;}
#kt_body .login_details_wrapper .login_input_wrapper input::placeholder{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 20px;font-weight: 400;line-height: 24px;}
.login_submit_wrapper button{width:100%;padding: 20px 0px;border-radius: 142px / 8px;}
.login_submit_wrapper button span{font-size:20px;font-weight:600}
.login_master_body{background: #611DB7;background: linear-gradient(90deg, rgba(97, 29, 183, 1) 0%, rgba(226, 19, 73, 1) 100%);padding: 0px 15px;}
.login_sec .forget_password a{color: #3E3E3E;font-family: 'leagueSpartan-Regular';font-size: 20px;font-style: normal;font-weight: 400;line-height: 24px;}
.login_sign_up_wrapper{display: flex;  align-items: center;  height: 100%;}
/*.login_sec {padding:100px 0px;}*/
.login_navbar_header .global_lang_wrap i {color:#fff;}
.login_sec {height: calc(100vh - 100px);background-image: url(/website/assets/images/login_back_img_updated.jpg);background-size: cover;background-position: center;background-repeat: no-repeat;overflow-y: auto
;}
.login_sec .padding_zero{padding:0;}
/**/
/*signup page css*/
.login_sec .login_input_wrapper.input_wrapper:has(.is-valid) .password-toggle{right: 8%;top: 25%;}
.login_sec .login_input_wrapper.input_wrapper:has(.is-invalid) .password-toggle{right: 8%;top: 25%;}
.login_sec .input_wrapper{position:relative}
.login_sec .input_wrapper i{position:absolute;right:5%;top:-4px;color: #3E3E3E;font-size: 14px;}
.forget_password.sign_up_wrapper{display:flex;justify-content:center;align-items:center;column-gap:10px;}
.forget_password.sign_up_wrapper h5{font-weight: 400;}
.login_sec .login_sign_up_wrapper .forget_password.sign_up_wrapper a{color: #C12A6D;font-family: 'leagueSpartan-Regular';}
.login_submit_wrapper{display:flex;flex-direction:column;row-gap:16px;}
.login_submit_wrapper .google_sign_up{border-radius: 4px;border: 1px solid rgba(0, 0, 0, 0.40);padding: 16px 0px;display:flex;justify-content:center;align-items:center;column-gap:16px;color: #3E3E3E;font-family: 'leagueSpartan-Medium';font-size: 16px;font-weight: 500;line-height: 16.8px;letter-spacing: 1.4px;}
.login_pg_img_wrapper {height:100%;}
/*select buyes css*/
.select_role_wrapper {display:flex;flex-direction:column;justify-content:center;align-items:center;row-gap:40px;height: 100%;}
.select_role_wrapper>h2{line-height: 30px;letter-spacing: 1.44px;font-family: 'leagueSpartan-Medium';font-size: 36px;}
.select_role_wrapper .buyer_wrapper{display:flex;align-items:center;column-gap:20px;}
.select_role_wrapper .select_buyer_input_wrapper label div img{width:100%;height:100%;object-fit:contain;}
.select_role_wrapper .select_buyer_input_wrapper label div{height: 170px;}
.select_role_wrapper .select_buyer_input_wrapper label{display:flex;flex-direction:column;row-gap:25px;border-radius: 10px;border: 1px solid #F2F2F2;background: #FFF;box-shadow: 0px 4px 4px 0px rgba(0, 0, 0, 0.05);padding: 35px;}
.select_role_wrapper .select_buyer_input_wrapper label h2{letter-spacing: 1.44px;line-height: 30px;font-family: 'leagueSpartan-Medium';}
.select_role_wrapper .select_buyer_input_wrapper input{display:none;}
.select_role_wrapper .select_buyer_input_wrapper input:checked+label{border: 2px solid #C12A6D;box-shadow: 0px 8px 38px 0px rgba(193, 42, 109, 0.20);}
.select_role_wrapper .select_continue_wrapper{display:flex;}
.select_role_wrapper .select_continue_wrapper button{padding: 20px 122px;}
.login_sec .iti.iti--allow-dropdown {width:100%;}
/*dropzone css*/
.login_sec .dz-button{margin:auto;}
.login_sec .dropzone.dz-clickable{border-radius: 10px;border: 1px dashed #BDBDBD;background: #FFF;padding: 50px 20px;}
.login_sec .dz-button i{color: #4A4A4A;font-size: 16px;}
.login_sec .dz-preview img{width:100%;height:100%;object-fit:cover;}
.login_sec .dz-preview .dz-error-mark{display:none !important;}
.login_sec .dropzone .dz-preview .dz-remove{z-index:999;}
.login_sec .dropzone.dz-clickable.dz-started .dz-error-message{display:none;}
.login_sec .dropzone .dz-button h5{color: #4A4A4A;font-family: 'leagueSpartan-Regular';font-size: 16px;font-weight: 400;margin-top:10px;}
/*loader functionality*/
.preloader {  width: 100%;  height: 100%;  top: 0;  position: fixed;  z-index: 99999;  background: #fff;  }
.preloader .cssload-speeding-wheel {  border: 0;  animation: unset;  }
.preloader .cssload-speeding-wheel {   display: flex;  justify-content: center;  flex-direction: column;  row-gap: 20px;  align-items: center;  height: 100%; }
.preloader .loader_img {
    width: 350px;
    /* height:120px; */
}
 .loading_icon {  display: flex;  justify-content:center;column-gap: 20px;}
.preloader .loader_img img {  object-fit: contain;width: 100%;height: 100%; }
 .loading_icon span:nth-child(1) {animation-delay: 0s;}
 .loading_icon span:nth-child(2) {animation-delay: 0.2s;}
 .loading_icon span:nth-child(3) {animation-delay: 0.4s;}
 .loading_icon span i{color: #C12A6D;font-size:15px }
 .loading_icon span { opacity: 0.6;animation: pulse 0.8s infinite ease-in-out;   ;}
@keyframes pulse {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);

    }
    50% {

        opacity: 1;
        transform: scale(1.2);
    }
}
/*Browse categories Dropdrown*/
.browse_categories_offcanvas .custom_mega_menu_list .dropdown select.accessories_select{border:none;background:none}
.browse_categories_offcanvas .custom_mega_menu_list .dropdown-menu{width:100%;box-shadow:none}
.browse_categories_offcanvas .offcanvas-header{justify-content: space-between}
.navbar .navbar_offcanvas i{font-size:14px}
.navbar .navbar_offcanvas {border: none;background: none;display: none;padding: 5px;border: 1px solid #c12a6d;border-radius: 5px;width: 30px;height: 30px;}
/*.preloader .loader_img{height: 00px;}*/
/**/
/*.custom_dropdown.website_dashboard_dropdown{display:none}*/
.classic_black_shirts shirts_long_desc.padding_zero .custom_flex{border-bottom: none !important;}
/*Footer Section*/
.footer .follow_us.custom_flex {margin-top: 10px;}
footer .follow_us.custom_flex i {color: #000;font-size: 14px;}
.footer .payment_images div{width: 55px;height: 30px;}
.footer .payment_images {border:0.5px solid #8e8e8e;;border-radius:4px;}
.footer .payment_images img {width: 100%;height: 100%;object-fit: contain;}
.footer .payment_methods {display: flex;flex-wrap:wrap;justify-content:space-between;column-gap:10px;row-gap: 10px;justify-content: center}
footer.footer {background: #F5F5F5;padding: 80px 0px;}
footer .footer_description a.navbar-brand.site_logo{width: 150px;height:60px;display: block}
footer .footer_description a.navbar-brand.site_logo img{object-position: left;width: 100%;height:100%;object-fit: contain}
.footer .custom_justify {padding-bottom: 30px;border-bottom: 1px solid #8e8e8e;margin-bottom: 20px;}
.footer .custom_justify h2 {text-transform: uppercase;}
.footer h6 {font-family: 'leagueSpartan-Bold';margin-bottom:20px;font-size:20px}
.footer_description p{max-width:85%;color:#000;line-height:22px;margin-top:15px}
.footer ul li{list-style:none}
.footer ul li a{color:#000;}
.footer ul {padding:0;display:flex;flex-direction:column;gap:20px}
.d-flex {  display: flex;  gap: 10px;}
/* Styling for each storage option */
#kt_body .storage-option {  border: 1px solid #C12A6D;  border-radius: 5px;  text-align: center;  cursor: pointer;  transition: all 0.3s ease-in-out;  display: inline-flex;  align-items: center;  justify-content: center;  }
#kt_body .custom_products_radio{flex-wrap: wrap;gap:15px;}

.varient_swiper_wrap_cont .custom_products_radio {max-height: 100px; overflow: auto;}

/* Hide the radio button */
.storage-option input[type="radio"] {  display: none;  }
/* Highlight when the radio input is checked */
.storage-option:has(input[type="radio"]:checked + span) {  color: #fff;  font-weight: bold;  background: #C12A6D;  }
/* Add border highlight for the selected option */
.storage-option input[type="radio"]:checked + label {  border: 1px solid #007bff !important;  /* Blue border for active */  color: #fff;  background: #007bff;  border-radius:5px  }
/*view-details page*/
.classic_black_shirts.product_detail_box::-webkit-scrollbar {width: 5px;}
.classic_black_shirts.product_detail_box::-webkit-scrollbar-track {background: #f1f1f1;}
.classic_black_shirts.product_detail_box::-webkit-scrollbar-thumb {background: #C12A6D;}
.classic_black_shirts.product_detail_box::-webkit-scrollbar-thumb:hover {background: #C12A6D;}
.classic_black_shirts.product_detail_box:hover{scrollbar-width: auto;scrollbar-color:auto;overflow-x: hidden;}
.classic_black_shirts.product_detail_box{overflow-y:scroll;max-height:600px;scrollbar-width: auto;scrollbar-color:auto;padding:0px 10px;overflow-x: hidden;}
.product_img_saleoff_wrapper.product_img_saleoff_wrapper_category {display:unset;}
.product_img_saleoff_wrapper.product_img_saleoff_wrapper_category .color_variation_wrapper{text-align:end;}
.product_details_inner_wrap .total_amount_view_det_pg {background-color:#c12a6d;padding:10px;border-radius:10px;text-align:end;}
.product_details_inner_wrap .total_amount_view_det_pg h4,.total_amount_view_det_pg h5{color:#fff;}
.view_details_tab_discrip .product_details_inner_wrap .table_wrapper table{width:100%;}
.view_details_tab_discrip .view_details_pg_product_details div label{color: #1C1C1C;font-family: 'leagueSpartan-Medium';font-size: 16px;font-weight: 500;}
.view_details_tab_discrip .view_details_pg_product_details div span{color: #1C1C1C;font-family: 'leagueSpartan-Regular';font-size: 16px;}
.view_details_tab_discrip .product_details_inner_wrap .custom_table{padding:20px 0px;border-top:1px solid #1C1C1C;border-bottom:1px solid #1C1C1C;border-radius:0px;margin:20px 0px;}
.view_details_sec .product_details_inner_wrap_one {border:1px solid #a56868;padding:10px;border-radius:10px;}
.view_details_sec .view_details_img_wrapper { display: flex; gap: 10px;flex-wrap: wrap;justify-content: end;}
.view_details_sec .view_details_img_wrapper .product_details_img img{border-radius:10px;}
/*.view_details_sec .view_details_img_wrapper .product_details_img {}*/
/*.view_details_sec .product_details_inner_wrap {display:flex;align-items:center;column-gap:50px;}*/
.view_details_sec .product_details_inner_wrap .product_details_img img{width:100%;height:100%;object-fit:cover;}
.view_details_sec .product_details_inner_wrap .product_details_img{height:120px;width:120px;box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);border-radius: 10px;}
.view_details_sec .product_details_inner_wrap .view_details_pg_product_details{display:flex;row-gap:10px;flex-direction:column;}
.view_details_sec{padding: 30px 0px 100px;background: rgb(250, 250, 250);}
.view_details_sec .view_details_tabs_wrapper{padding:20px 0px;}
.view_details_sec .view_details_tabs_wrapper ul{border:0px;column-gap: 5px;}
.view_details_sec .view_details_tabs_wrapper ul li{border-bottom:1px solid #c12a6d;}
.view_details_sec .view_details_tabs_wrapper ul li button{color:#3E3E3E; font-family: 'leagueSpartan-Medium';  font-size:20px;  letter-spacing:0.42px;  line-height:23px;}
.view_details_sec .view_details_tabs_wrapper ul li button.active{background-color:#c12a6d;color:#fff;}
/*.view_details_pg_product_details{max-width:50%;}*/
.view_details_sec .product_details_inner_wrap div label{font-family: 'leagueSpartan-SemiBold';font-size:18px;line-height:21px;letter-spacing:0.42px;}
.view_details_sec .product_details_inner_wrap div span{font-family: 'leagueSpartan-Light';font-size:18px;line-height:21px;letter-spacing:0.42px;}
.view_details_sec .table_wrapper table{width:100%;}
.total_amount_view_det_pg h4 {text-align:end;background-color:#c12a6d;padding:10px;color:#fff;border-radius:10px}
.total_amount_view_det_pg h5{text-align:end;padding-top:10px;}
.total_amount_view_det_pg{padding-top:50px;}
.custom_row_view_edit{align-items: center;}
.view_details_sec .view_details_tabs_wrapper ul li button:hover{border-bottom: 1px solid #c12a6d;border: 1px solid #ffffff00;}
.view_details_sec .view_details_tabs_wrapper ul li {  animation: scaleOpacity 3s ease-in-out infinite;  }
@keyframes scaleOpacity {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.05);
        opacity: 0.9;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}
.custom_dropdown.custom_simple_select_dropdown .select2-container--bootstrap5 .select2-selection--single .select2-selection__rendered{max-width: 123px;}
#kt_body .login_details_wrapper .login_input_wrapper:has(input.is-invalid) i{right:10%;top:25%;}
.account_settings .profile_settings_wrapper .txt_field:has(.is-invalid) i{top: 49%;}
.view_details_sec .custom_table {border-top:1px solid black;border-radius:0px;border-bottom:1px solid black;padding:30px 0px;}
/*category slider css*/
.category_slider{padding-bottom: 50px;}
.category_slider_img img{width:100%;height:100%;object-fit:contain;border-radius:50%;transition: transform 0.3s ease;object-position: center;}
.category_slider_wrapper{padding:20px 0px;}
.category_slider_sec{padding:30px 0px;}
.category_slider_img:hover img {  transform: scale(1.2);  }
.category_slider_img{box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);width:200px;height:200px;margin:auto;border-radius:50%; position: relative;overflow: hidden;}
.img_text_overlay p{color:#fff;font-family: 'leagueSpartan-SemiBold';font-size:22px;line-height:1.2;text-align:center;}
.img_text_overlay {  position: absolute;  top: 0;  left: 0;  right: 0;  bottom: 0;  background: rgba(0, 0, 0, 0.5); /* Semi-transparent overlay */  display: flex;  justify-content: center;  align-items: center;  opacity: 0; /* Initially hidden */  transition: opacity 0.3s ease;  }
.category_slider_img:hover .img_text_overlay {opacity: 1; /* Show overlay on hover */}
/*add to cart sweet alert*/
.add_to_cart_swal {overflow:hidden;display: flex;justify-content: space-between;margin: 20px 0px 0px 0px;}
.add_to_cart_swal .btn_pink.guest-checkout-button{border:0px;}
.guest_swal_wrrapper {display: flex;flex-direction: column;row-gap: 10px;}
/*notfication page css*/
.notification_menus_wrapper{display: flex;align-items: center;position:relative;}
.notification_menus_wrapper .custom_badge{position:absolute;top:-10%;left:5%;background-color:#EF4444;width:15px;height:15px;border-radius:50%;display:flex;align-items:center;justify-content:center;color:#fff;font-size:10px;}
.mark_all_read_btn {display:flex;justify-content:space-between;align-items:center;}
.notification_user_det_wrap .client_noti_img_wrap img{width:100%;height:100%;object-fit:cover;}
.notification_user_det_wrap .client_noti_img_wrap{width: 32px;height: 32px;}
.notification_user_det_wrap .notifications_img_whole_wrapper{ width: 52px;height: 52px;border: 1px solid #E3E3E3;border-radius: 50%;display:flex;justify-content:center;align-items:center;}
.notification_user_det_wrap .notification_img_and_title{display:flex;align-items:center;column-gap:10px;}
.notification_user_det_wrap{display:flex;justify-content:space-between;align-items:center;border-bottom: 0.5px solid #DBDBDB;padding: 15px 20px 15px 10px;}
.notification_user_det_wrap .notification_client_time_wrap{display:flex;align-items:center;column-gap:10px;}
.notification_user_det_wrap .notification_client_time_wrap i{color: var(--orange_color);font-size: 12px;}
.notification_pg_wrapper {display:flex;flex-direction:column;row-gap:15px}
.notification_user_det_wrap:last-child{border:0px;}
.notification_client_name_det h6{color: #1C1C1C;}
.notification_client_name_det p{color: var(--dark_grey_color);font-weight: 500;font-family: 'LeagueSpartan-Medium';font-size: 16px;}
.notification_user_det_wrap .notification_client_time_wrap h6{color: var(--dark_grey_color);leading-trim: both;text-edge: cap;font-family: 'LeagueSpartan-Medium';font-size: 12px;font-weight: 500;}
#kt_app_root .notification_wrapper.custom_notification {  border-radius: 15px;  box-shadow: 0px 6px 40px 0px rgba(0, 0, 0, 0.15);  width: 50%;  position: absolute;  transform: translate(-35px, 65px) !important;  }
/*product slider css*/
.product_gallery .swiper-slide {height: 100%; opacity: 0.4; cursor: pointer;}
.thumbnail_slider_wrapper .product_gallery{padding:20px 0px;}
.product_gallery .swiper-slide-thumb-active {opacity: 1;}
.product_gallery .swiper-slide img { width: 100%;height: 100%;object-fit: cover;box-shadow: 0px 0px 4px 0px rgba(0,0,0,0.25);border-radius: 10px;}
.product_gallery_two .swiper-slide img{width: 100%;height: 100%;object-fit: contain;box-shadow: 0px 0px 10px 0px rgba(0,0,0,0.75);}
.product_img_slider_wrap {  width: 100%;  height: 150px; position: relative;}
.main_slider_product_img_wrapper {width: 100%;height: 450px;}
.product_img_slider_wrap.more_slide{position: relative; cursor: pointer;}
.main_product_slider_wrapper .swiper-button-next,.main_product_slider_wrapper .swiper-button-prev{color:#C12A6D;z-index:99;}
.main_slider_product_img_wrapper img { transition: transform 0.5s ease-in-out, opacity 0.5s ease-in-out;}
.main_product_slider_wrapper .swiper-slide-active .main_slider_product_img_wrapper img {opacity: 1;}
/*new product design*/
.varient_swiper_wrap_cont .storage-option  span{font-size:10px;}
#kt_app_root .varient_swiper_wrap_cont .custom_products_radio label{padding:5px;}
#kt_app_root .varient_swiper_wrap_cont .custom_products_radio{display:flex;gap:10px}
.varient_swiper_wrap_cont{padding-bottom:20px;}
/*.varient_swiper_wrap_cont .more-options{color:#C12A6D;font-weight: 900;font-size: 15px;}*/
.varient_swiper_wrap_cont .more-options span{background-color:black;padding:2px;width:5px;height:5px;display:inline-block;border-radius:50%;}
.varient_swiper_wrap_cont .more-options{display:flex;align-items:center;column-gap:3px;}
.single_pro_color_swiper2 input[type="radio"]:checked::before {content: "";position: absolute;top: 0;bottom: 0px;left: 0px;right: 0px;width: 100%;height: 100%;border: 1px solid #fff;background-color: rgba(0, 0, 0, 0.125);border-radius: 50%;}
.single_pro_color_swiper2 input[type="radio"]:checked{transform: scale(1.3)}
.single_pro_color_swiper2 .round_color input[type="radio"]{visibility: unset}
.single_pro_color_swiper2 input[type="radio"] {position: relative;transition: all 0.2s ease;appearance: none;width: 20px;height: 20px;border: 1px solid #000;border-radius: 50%;cursor: pointer;}
.single_pro_color_swiper input[type="radio"] {position: relative;transition: all 0.2s ease;appearance: none;width: 20px;height: 20px;border: 1px solid #000;border-radius: 50%;cursor: pointer;}
.single_pro_color_swiper2{margin: 0px 35px;padding:10px 0px;position: unset;text-align: center;}
.single_pro_color_swiper input[type="radio"]:checked {transform: scale(1.3);}
.single_pro_color_swiper input[type="radio"]:checked::before {content: "";position: absolute;top: 0;bottom:0px;left: 0px;right:0px;width: 100%;height: 100%;border:1px solid #fff;background-color: rgba(0, 0, 0, 0.125);border-radius: 50%;}
.single_pro_color_swiper_arrow,.single_pro_color_swiper_arrow.swiper-button-disabled{color:#000;}
.single_pro_color_swiper{margin: 0px 35px;padding:10px 0px;position: unset;text-align: center;}
.single_pro_color_slider_wrap{position: relative;}
.swiper-button-next.single_pro_color_swiper_arrow{right:-5px;}
.swiper-button-prev.single_pro_color_swiper_arrow{left:0px;}
.single_pro_color_swiper_arrow:after{font-size:18px;font-weight:900;}
.single_pro_color_swiper .round_color input[type="radio"] {visibility: unset;}
.single_product_wrapper:has(.select_classic_shirts_colors) .select_classic_shirts_colors{display:block}
.product_heading_dropdown_wrap{display:flex;justify-content:space-between;align-items:end;column-gap:15px}
.product_heading_dropdown_wrap .dropdown-toggle::after{display:none;}
.product_heading_dropdown_wrap .dropdown-toggle i{font-size:15px;color:#000;}
.add_to_crt_counter_plus {padding-top: 10px;display: flex;column-gap: 10px;align-items: center;justify-content: space-between;}
.add_to_crt_counter_plus .counter_product_wrap {display: flex;flex: 1;}
.add_to_crt_counter_plus .cart_btn{flex:2;}
.add_to_crt_counter_plus .add_to_cart_form {flex: 2;}
.add_to_crt_counter_plus .add_to_cart_form button.cart_btn {width: 100%;}
.add_to_crt_counter_plus .counter_product_wrap .counter_product_wrap_input {width: 50%;text-align: center;border: 1px solid #c9c0c0;}
.counter_product_wrap_plus_minus {display:flex;flex-direction:column;}
.counter_product_wrap_plus_minus a{padding: 2px 10px; background-color: #e6e6e6; border: 1px solid #afa9a9;color:#000;font-size:16px;font-weight:900;line-height: 12px;}
.add_to_crt_counter_plus .cart_btn img {width: 20px;height: 20px;}
.plus_btn_single_product_pg i {color: #C12A6D;padding: 10px;}
.product_rating_price_text_wrap {display:flex;justify-content:space-between;align-items:end;}
.product_rating_price_text_wrap .rating_lst_week_wrap{display:flex;flex-direction:column;row-gap:6px;}
.plus_btn_single_product_pg {border: 1px solid #C12A6D;width: 20px;height: 20px;border-radius: 50%;display: flex;justify-content: center;align-items: center;}
/*chatbot home screen set css*/
.chatbot_container { max-width: 100vw;  min-width: 24%;  max-height: 100vh; min-height: calc(100vh - 101px); height: calc(100vh - 101px); border: 1px solid var(--pink); border-radius: 10px;  width: 24%; display: inline-block; position: fixed; bottom: 5px; /* margin: 0px 0px 50px 0px; */ vertical-align: top; right: 0px; z-index: 10; }
.inner_main_wrap{display: inline-block;width: 75%;}
.outer_main{display:inline-block}
.outer_wrap{display:flex;column-gap: 1%;}
/*#chatbot-container { position: fixed; bottom: 20px; right: 20px; width: 300px; height: 400px; background-color: #f9f9f9; border: 1px solid #e5e5e5; border-radius: 10px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); display: flex; flex-direction: column; z-index: 1000; }*/
.chatbot-header {background-color: rgb(249, 249, 249); color: white; padding: 10px; font-size: 16px; display: flex; justify-content: space-between; align-items: center; border-top-left-radius: 10px; border-top-right-radius: 10px; }
.chatbot-body {display: flex; flex-direction: column-reverse;  height: calc(100% - 110px); padding: 10px; overflow-y: auto; background-color: #f9f9f9; }
.chatbot-footer {position:relative; display: flex; padding: 10px; background-color: #fff; border-bottom-left-radius: 10px; border-bottom-right-radius: 10px; border-top: 1px solid #e5e5e5; }
.user-input { background-color: #f0f3f3;width: 100%; padding: 10px; border: 1px solid #9c2d5f; border-radius: 5px; margin-right: 10px; font-size: 14px; }
.send-btn {height: 40px;width: 40px; background-color: #31a231;color: white;padding: 10px;display: flex;justify-content: center;align-items: center;border-radius: 50%;border: 1px solid #31a231; cursor: pointer; }
/*.send-btn:hover { background-color: #9c2d5f; }*/
/*.send-btn:hover i{ color:#fff ;}*/
.upload_message_wrap_img_sec{display: none;}
.upload_message_wrap_img img{width:100%;height:100%;object-fit:cover;border-radius:10px;box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);}
.upload_message_wrap_img{width:100px;height:100px;padding:10px 10px}
.upload_message_wrap_img_sec{position:absolute;bottom:100%;right:0;left:0;background-color:#ffffffb8;}
.upload_img_btn_chatbot{position:absolute;border:0px;right: 67px;top:33%;background:transparent}
.upload_img_btn_chatbot i{color:#9c2d5f;font-size:15px}
.chatbot-footer .user-input{padding-left:30px;padding-right: 35px;}
.chatbot-footer .search_icon_chatbot{position:absolute;top:40%;left: 23px;color:#000}
.upload_img_btn_chatbot input[type="file"]{display:none}
.chatbot-header{padding: 0; justify-content: center;}
.chatbot-header button { top: -23px; padding: 0;position: relative;z-index:99}
.close-chat-btn i{font-size: 24px;background-color: #c12a6d;color: white;padding: 10px 15px;border-radius:10px;box-shadow: 0 2px 5px rgba(0,0,0,0.2);}
.chatbot-header span i{color:#ffffff;font-size:22px;margin-right:10px;}
.send-btn i{color:#000;font-size:16px;}
.close-chat-btn { background: none; border: none; color: #C12A6D; font-size: 18px; cursor: pointer; }
.close-chat-btn:hover { color: #ffeb3b; }
.messages { display: flex; flex-direction: column; }
.user-message, .bot-message { max-width: 75%; padding: 10px; margin: 5px 0; border-radius: 10px; font-size: 14px; display: inline-block; }
.user-message {word-break: break-word; background-color: #9c2d5f; color: white; align-self: flex-end; text-align: right; }
.bot-message { background-color: #f1f1f1; color: #333; align-self: flex-start; text-align: left; }
.chat-loader { display: none; margin-top: 10px; text-align: center; }
.chat-loader span { display: inline-block; width: 10px; height: 10px; margin: 0 5px; background-color: #ff0073; border-radius: 50%; animation: bouncing 1s infinite alternate; }
.chat-loader span:nth-child(1) { animation-delay: 0.2s; }
.chat-loader span:nth-child(2) { animation-delay: 0.4s; }
.chat-loader span:nth-child(3) { animation-delay: 0.6s; }
.chat-loader span:nth-child(4) { animation-delay: 0.8s; } @keyframes bouncing { 0% { transform: translateY(0); } 100% { transform: translateY(-10px); } }
.clear-chat-btn {width: 100%;background-color: #f9f9f9;border: none;padding: 5px 10px;font-size: 14px;cursor: pointer;color: #9c2d5f;transition: 0.5s ease all;}
.chatbot_icon_toggle i {color: #fff;font-size: 25px;}
.chatbot_icon_toggle {z-index: 99;display:none;background-color: #9c2d5f;height: 50px;width: 50px;position: fixed;bottom: 3%;right: 2%;border-radius: 50%;border: 0;box-shadow: 0px 0px 5px 0px rgba(0,0,0,0.75);}
/*avatar animation for chatbot*/
.chat_bot_scrolling_img img{width:75%;height:100%;object-fit:cover;}
.chat_bot_scrolling_img{height:500px}
.chat_bot_scrolling_img {position: fixed;width: 100%;height: 100%;top: 0;pointer-events: none; /* important so chat stays clickable */z-index: 1;}
.chatbot-body:has(.user-message) .chat_bot_scrolling_img{display:none;}
.custom_mega_menu_list.responsive_mega_menu,.product_search_togglers .responsive_hidden.custom_product_search {display: none;}

.cartoon-rotation {
    width: 100%;
    height: 100%;
    position: relative;
}
.cartoon-rotation  img{
    width: 100%;
    height: 100%;
    object-fit: contain;
    position: absolute;
    left: 0;
    top: 0;
}
.frame {
    width: 100%;
    height: auto;
    opacity: 0; /* Start with all frames hidden */
    transition: opacity 0.3s ease;
}

.frame.active {
    opacity: 1; /* Make active frame visible */
}

.cartoon-rotation-img {
    animation-play-state: paused; /* Pause the GIF by default */
}

/**/
 body .responsive_hidden{display:none ;}
.responsive_top_header{display:none;}
.product_search_togglers .responsive_hidden.custom_product_search{width:100%}
.product_search_togglers  .custom_product_search.responsive_hidden .product_search.txt_field:before{display: none}
.product_search_togglers{ display: flex;width: 100%;align-items: center;gap: 20px;flex-direction: row-reverse;}
.responsive_top_header .dropdown_resp_profile_image{width:35px;height:35px;border-radius:50%;overflow: hidden}
.responsive_top_header .dropdown_resp_profile_image img{width:100%;height:100%;object-fit:cover}
.responsive_top_header .dropdown{display: flex;align-items: center;gap:5px}
.responsive_top_header .dropdown button{background:none;border:none}
.responsive_top_header .cart_numbers{position:relative}
.responsive_top_header .cart_numbers i{font-size:18px;color:#C12A6D}
.responsive_top_header .cart_numbers span{position:absolute;background:#C12A6D;width:15px;top:-18px;left:10px;border-radius:50%;display:flex;justify-content:center;align-items:center;font-size:10px;color:white}
/*add to cart modal css*/
.product_img_add_to_cart img{width:100%;height:100%;object-fit:contain;}
.product_img_add_to_cart{height:100px;width:100px;}
.pro_img_details_add_crt_wrap .product_details_addtocart p{font-weight:500}
.pro_img_details_add_crt_wrap .product_details_addtocart p:has(span){font-weight:600}
.pro_img_details_add_crt_wrap .product_details_addtocart p span{color:#000;font-weight:500}
.pro_img_details_add_crt_wrap .product_details_addtocart{display:flex;flex-direction:column;row-gap:5px;}
.pro_img_details_add_crt_wrap{display:flex;align-items:center;column-gap:35px;padding-bottom:20px;}
.size_add_crt_wrap{border-top:1px solid #ccc;padding-top:20px;display:flex;flex-direction:column;row-gap:15px;}
.size_add_crt_wrap .size_addtocrt_dropdown_wrap select{width:fit-content;background-color: #f0f2f2;padding: 8px 24px 8px 10px;}
.size_add_crt_wrap .size_addtocrt_dropdown_wrap{display:flex;flex-direction:column;row-gap:5px}
.modal_footer.modal_footer_add_to_cart {justify-content: right;column-gap: 10px;}
.modal_header_addtocart{background-color:#f0f2f2; }
/*user+dropdwon*/
.user_details_img_wrap img{width:100%;height:100%;object-fit:cover;border-radius:50%;}
.user_details_img_wrap{width:50px;height:50px;}
.website_dashboard_dropdown {position:relative}
.website_dashboard_dropdown .dropdown{position:unset;}
.website_dashboard_dropdown .dropdown-menu.show{max-height: unset;overflow-y: unset;top: 55px;left: unset;right: 0;width: 350px;padding:20px 0px}
.user_edit_img_wrap .user_details_wrap{display:flex;column-gap:10px;}
.user_edit_img_wrap .user_details_wrap .user_name p{display:flex;column-gap:10px;color: #e1b844;}
.user_edit_img_wrap .user_details_wrap .user_name p i{color: #e1b844;}
.user_edit_img_wrap{display:flex;justify-content:space-between;align-items:center}
.log_out_wrap {padding-top:10px;border-top:1px solid #dbd8d8;}
.website_dashboard_dropdown .dropdown-menu li a{display:flex;align-items:center;column-gap:10px;}
.website_dashboard_dropdown ul.dropdown-menu.show li a:hover i{color:#fff}
.website_dashboard_dropdown .dropdown-menu.show li{padding:10px 20px 10px 20px}
.website_dashboard_dropdown .dropdown-menu.show li:last-child{padding:10px 20px 0px 20px}
.website_dashboard_dropdown .dropdown-menu.show li:has(.user_edit_img_wrap){border-bottom:1px solid #dbd8d8;}
.website_dashboard_dropdown .dropdown-menu.show li:first-child {padding: 0px 20px 20px 20px}
#kt_body .storage-option.storage-option_modal_inner{padding:5px;}
.size_add_crt_wrap .round_color {position:relative;}
.waist_number_wrapper.members.add_to_cart_size_wrap {display:flex;flex-wrap:wrap;gap:10px;}
.round_color.round_color-add_to_cart label{height:20px;width:20px;}
.round_color.round_color-add_to_cart label:after{top: 5px; left: 3px;}
.select_classic_shirts_colors:has(.round_color-add_to_cart){margin:0px;}
.row_gap_addtocart_varients{display:flex;flex-direction:column;row-gap:5px;}
.row_gap_addtocart_varients p{font-weight:600}
.send-btn img{width:40px;height:40px;object-fit:contain;}
.upload_img_chatbot_img_icon img{width:20px;height:20px;object-fit:contain}
/*.product_sale_sec .super_deals_slider_wrapper .swiper-slide {overflow: hidden;position: relative;z-index: 1;}*/
#kt_body .single_product_wrapper .dropdown-menu {left: unset !important;right: 0px !important;}


.resize-handle {position: absolute;z-index: 11;background: transparent;}
.resize-top, .resize-bottom {height: 10px;left: 0;right: 0;cursor: ns-resize;}
.resize-top {top: 0;}
.resize-bottom {bottom: 0;}
.resize-left, .resize-right {width: 10px;top: 0;bottom: 0;cursor: ew-resize;}
.resize-left {left: 0;}
.resize-right {right: 0;}

/* Corner handles */
.resize-top-left,.resize-top-right,.resize-bottom-left,.resize-bottom-right {width: 15px;height: 15px;z-index: 12;background: transparent;}
.resize-top-left {top: 0;left: 0;cursor: nwse-resize;}
.resize-top-right {top: 0;right: 0;cursor: nesw-resize;}
.resize-bottom-left {bottom: 0;left: 0;cursor: nesw-resize;}
.resize-bottom-right {bottom: 0;right: 0;cursor: nwse-resize;}
#loader {text-align: center;margin: 20px 0;}


/**/





.cartoon-rotation{transition: 0.5s ease all;margin-left:auto;}
.chat_start {width:70px;height:70px;transition: 0.5s ease all;}

.cart_modal .modal-dialog { max-width: 1024px; }
.cart_modal .modal-header { background-color: var(--pink); }
.cart_modal .modal-header .btn-close { color: #ffff;-webkit-filter: invert(100%);filter: invert(100%);opacity:1;}
.cart_modal .modal-body { padding: 20px 10px; }
.cart_modal .modal-body .product_detail_sec { padding: 0; }
.cart_modal .modal-body .product_detail_sec .main_slider_product_img_wrapper { height: 350px; }
.cart_modal .modal-body .product_detail_sec .product_img_slider_wrap { height: 100px; }
.cart_modal .modal-body .product_detail_sec .product_detail_box { min-height: 100%; max-height: 490px; display: flex; flex-direction: column; /*justify-content: center;*/ }
.cart_modal .modal-body .product_detail_sec .classic_black_shirts .add_tag_btn { padding-bottom: 0px; padding-top: 30px; }
.cart_modal .modal-body .product_detail_sec .thumbnail_slider_wrapper .product_gallery { padding-bottom: 0; }
.cart_modal .modal-body .product_detail_sec .container .row {align-items: center;}

.select2-container:has(.select2_custom_class) {width: 64% !important;left: 28% !important;right: 5px;}
.select2_custom_class{width:100% !important;border: 2px solid #C12A6D !important;border-radius:0px 0px 3px 3px !important;top: -2px;z-index: 99;}
.mega_list .select2-container--open {border-top: 2px solid #C12A6D;border-right: 2px solid #C12A6D;border-left: 2px solid #C12A6D;border-radius:3px 3px 0px 0px;border-bottom: 0;padding:5px 10px 5px 10px;z-index: 999;background-color: #fff;}
.select2_custom_class.select2-dropdown .select2-search .select2-search__field{border: 2px solid #C12A6D !important;border-radius:7px !important;}
.select2_custom_class.select2-dropdown .select2-results__option.select2-results__option--selected:after{background-color:#fff;}

/* Toggle Switch (with label) */
/* Container styles for switch */
.switch-toggle {margin-bottom:10px;height: 45px;padding: 0px;border-radius: 4px;position: relative;background: rgba(227, 229, 232, 0.5);font-size: 16px;font-weight: 500;line-height: normal;font-style: normal;}
.switch-toggle input[type="checkbox"] {cursor: pointer;position: absolute;inset: 0;appearance: none;z-index: 2;}
.swal2-icon.swal2-error.swal2-icon-show{margin: 0 auto}
.swal2-container .swal2-html-container{max-height: 450px}
/*.switch-toggle input[type="checkbox"] + label.switch-toggle-label span:nth-child(1){color:white !important;}*/
/* Label styles */
.switch-toggle {
    position: relative;


    user-select: none;
}

/* Label container */
.switch-toggle input[type="checkbox"] + label.switch-toggle-label {
    position: relative;
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 1fr;
    gap: 2px;
    place-items: center;
    padding: 12px 0;
    color: white;
    cursor: pointer;
    border-radius: 4px;
    background: #e8e8e8; /* default background */
    overflow: hidden;
}

/* The sliding background */
.switch-toggle input[type="checkbox"] + label.switch-toggle-label:before {
    content: "";
    position: absolute;
    top: 2px;
    bottom: 2px;
    left: 2px;
    width: 50%;
    background: #C12A6D; /* red background */
    border-radius: 4px;
    box-shadow: 0px 10px 20px 0px rgba(16, 39, 68, 0.1);
    transition: transform 250ms cubic-bezier(0.93, 0.26, 0.07, 0.69);
    z-index: 0;
}

/* Move the red background to the right when checked */
.switch-toggle input[type="checkbox"]:checked + label.switch-toggle-label:before {
    transform: translateX(100%);
}

/* Span text should be above the sliding background */
.switch-toggle input[type="checkbox"] + label.switch-toggle-label span {
    position: relative;
    z-index: 1;
    transition: color 200ms linear;
}

/* Default text colors */
.switch-toggle input[type="checkbox"] + label.switch-toggle-label span:nth-child(1) {
    color: white; /* Guest Login text default white */
}

.switch-toggle input[type="checkbox"] + label.switch-toggle-label span:nth-child(2) {
    color: var(--dark_grey_color);
}

/* When toggled, swap text colors */
.switch-toggle input[type="checkbox"]:checked + label.switch-toggle-label span:nth-child(1) {
    color: var(--dark_grey_color); /* Guest Login text gray when unchecked */

}

.switch-toggle input[type="checkbox"]:checked + label.switch-toggle-label span:nth-child(2) {
    color: white; /* Existing Buyer text white when checked */
}

.guest_swal_wrrapper .existing_user .txt_field1{margin-bottom:12px}
.cart_btn.btn_main.btn_pink.add_to_cart_button img{width: 20px;height: 20px;}
.cart_btn.btn_main.btn_pink.add_to_cart_button{line-height:1.7em}
.add_to_crt_counter_plus .btn_main.cart_btn.add_to_cart_button{flex:3}
.product_search.txt_field .dropdown-menu.product_list_dropdown li.dropdown-item{display:flex;align-items:center;column-gap:10px}
.product_search.txt_field .dropdown-menu.product_list_dropdown li.dropdown-item i{font-size:14px;color:black}
.quanitity_counter  .add_to_crt_counter_plus .counter_product_wrap .counter_product_wrap_input{width:12%}
.product_description .chat_icon i{font-size:18px;color:var(--pink)}
.txt_field.phone_number_wrapper_setting {display:flex;flex-direction:column;}
.product_detail_sec .classic_black_shirts  .quanitity_counter{margin-top:15px}

/*chat*/
.chat_sec.custom_chats { font-family: 'Segoe UI', Arial, sans-serif; background-color: #f5f5f5; height: 100vh; padding: 20px 0; }
.chat_sec.custom_chats .chats_detail { background: white; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
.chat_sec.custom_chats .Chat_box .vertical-tabs.nav-detail{height:500px;overflow-y: auto}
.chat_sec.custom_chats .Chat_box .chats_detail { height: 97%; border-right: 1px solid #eaeaea; display: flex; flex-direction: column; }
.chat_sec.custom_chats .Chat_box .search-btn-container { padding: 15px; border-bottom: 1px solid #eaeaea; }
.chat_sec.custom_chats .Chat_box .custom_search_box { border-radius: 20px; padding: 8px 15px; border: 1px solid #ddd; width: 100%; }
.chat_sec.custom_chats .Chat_box .new_chat { padding: 15px; border-bottom: 1px solid #eaeaea; }
.chat_sec.custom_chats .Chat_box .new_chat h3 { font-size: 18px; font-weight: 600; margin: 0; }
.chat_sec.custom_chats .Chat_box .chat_users_show { flex: 1; display: flex; flex-direction: column; }
.chat_sec.custom_chats .Chat_box .contacts a.chat-box-toggle { padding: 12px 15px; border-bottom: 1px solid #f0f0f0; transition: background 0.2s; }
.chat_sec.custom_chats .Chat_box .contacts:hover {background-color: #f9f9f9;}
.chat_sec.custom_chats .Chat_box .user_profile_msg { display: flex; align-items: center; text-decoration: none; color: #333;gap: 10px; }
.chat_sec.custom_chats .Chat_box .user_img {width: 42px;height: 42px;border-radius: 50%;border: 1px solid silver;}
.chat_sec.custom_chats .Chat_box .user_img .avatar {width: 100%;height: 100%;border-radius: 50%;object-fit: cover;}
.chat_sec.custom_chats .Chat_box .user_msg h6 { font-size: 14px; margin: 0 0 3px 0; font-weight: 600; }
.chat_sec.custom_chats .Chat_box .user_msg p { font-size: 12px; color: #777; margin: 0; }
.chat_sec.custom_chats .Chat_box .users_chats { height: 100%; display: flex; flex-direction: column; position: relative;background: white;border-radius: 10px;box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);}
.chat_sec.custom_chats .Chat_box .profile { padding: 15px; border-bottom: 1px solid #eaeaea; display: flex; align-items: center;gap:10px; }
.custom_scroll {scrollbar-color: var(--pink) #F5F5F5;scrollbar-width: thin;}
.custom_scroll::-webkit-scrollbar {width: 10px;background-color: #F5F5F5;border-radius: 8px;}
.custom_scroll::-webkit-scrollbar-thumb {background-color:var(--pink);border-radius: 8px;border: 2px solid #F5F5F5;}
.custom_scroll::-webkit-scrollbar-thumb {transition: background-color 0.3s ease;}
.custom_scroll::-webkit-scrollbar-track{-webkit-box-shadow: inset 0 0 6px rgba(0,0,0,0.3);border-radius: 10px;background-color: #F5F5F5;}
.chat_sec.custom_chats .Chat_box .btn_black { background: none; border: none; color: #333; font-size: 16px; cursor: pointer; }
.chat_sec.custom_chats .Chat_box .product_details { display: flex; align-items: center; flex-grow: 1; }
.chat_sec.custom_chats .Chat_box .desc { margin-left: 10px; flex-grow: 1; }
.chat_sec.custom_chats .Chat_box .desc h5 { font-size: 16px; margin: 0 0 3px 0; }
.chat_sec.custom_chats .Chat_box  .desc .bid { font-size: 12px; color: #777; }
.chat_sec.custom_chats .Chat_box .user_name {display: none;}
.chat_sec.custom_chats .Chat_box .vertical-content { overflow-y: auto; padding: 15px; background-color: #f9f9f9; height:500px;overflow-y: auto}
.chat_sec.custom_chats .Chat_box .get_all { display: flex; flex-direction: column; }
.chat_sec.custom_chats .Chat_box .get_all p span{text-align:right}
.chat_sec.custom_chats .Chat_box .get_all p { margin: 0 0 10px 0; padding: 8px 12px; background: white; border-radius: 8px; max-width: 70%; margin-left: auto; border-radius: 0px 10px 10px 10px;box-shadow: 0px 4px 20px 0px rgba(0, 0, 0, 0.10);display: flex;flex-direction: column;gap:10px;}
.chat_sec.custom_chats .Chat_box .get_all p strong {color: #555;}
.chat_sec.custom_chats .Chat_box .preview_upload_file .image-preview{width:200px;height:200px}
/*.chat_sec.custom_chats .Chat_box .preview_upload_file { padding: 10px 15px; border-top: 1px solid #eaeaea; border-bottom: 1px solid #eaeaea; font-size: 12px; color: #777; position: absolute;bottom: 75px;width:100%;background: #fff}*/
.chat_sec.custom_chats .Chat_box .send_msg {padding: 15px;}
.chat_sec.custom_chats .Chat_box .input-group {display: flex;align-items: center;gap:10px;}
body #kt_app_root .chat_sec.custom_chats .Chat_box .text_message_area {resize: none;border-radius: 50px;background: #cbd7dd5c;padding: 15px 20px;border: 1px solid #CECECE;box-shadow: 0px 2px 25px 0px rgba(0, 0, 0, 0.02) inset;color: #4A4A4A; }
body #kt_app_root .chat_sec.custom_chats .Chat_box .send-button { border-radius: 20px; padding: 10px 20px; background-color:var(--pink); color: white; border: none; cursor: pointer; }
.chat_sec.custom_chats .Chat_box .send-button:hover {background-color: var(--pink);}
.chat_sec.custom_chats .Chat_box .user_msg h6 {font-weight: 500;}
.chat_sec.custom_chats .Chat_box .task-item {display: flex;align-items: center;padding: 8px 15px;font-size: 14px;}
.chat_sec.custom_chats .Chat_box .task-item input[type="checkbox"] {margin-right: 10px;}
.chat_sec.custom_chats .Chat_box .task-meta {font-size: 11px;color: #999;margin-top: 3px;}
.chat_sec.custom_chats .Chat_box .all_users_chats li.active a.chat-box-toggle {background: #c0c0c052;}
.custom_chats .chat_messages {padding: 20px 10px 20px 20px;height: 475px;}
body #kt_app_root .chat_sec.custom_chats .Chat_box .input-group button.upload-button {position: absolute;right: 13%;padding: 10px;background: var(--pink);border-radius: 50%;display: flex;align-items: center;justify-content: center;width: 40px;height: 40px;color: white;}
body #kt_app_root .chat_sec.custom_chats .Chat_box .input-group button.upload-button i {color: white;padding: 0;}
body #kt_app_root .chat_sec.custom_chats .Chat_box .send-button i {color: white;}
.chat_back_btn {border-radius: 50%;padding: 0;width: 35px;height: 35px;}
.chat_sec.custom_chats .Chat_box .profile .chat_back_btn i {margin: 0;}
.english_arabic_converter_wrap select{ position: absolute;top: 0;left: 0;width: 100%;height: 100%;opacity: 0;cursor: pointer;z-index: 2;}
.global_lang_wrap{font-size: 22px;pointer-events: none;position: absolute;top: -2px;right: -5px;z-index: 1;}
.global_lang_wrap i{color:#C12A6D;font-size:18px;}
.english_arabic_converter_wrap{position: relative; display: inline-block; width: 30px; height: 30px;}
.super_deals_slider_wrapper.you_may_like_this .swiper-slide{height: auto !important;}
.sparkle-button {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: transparent;
    border: none;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.sparkle-button:hover {
    transform: scale(1.05);
    background: linear-gradient(135deg, #C12A6D 0%, #A82459 50%, #8F1E45 100%);
}

.sparkle-button:hover .sparkle-icon {
    transform: rotate(180deg) scale(1.1);
}

.sparkle-button:hover .sparkle-icon::before,
.sparkle-button:hover .sparkle-icon::after {
    background-color: white;
}

.sparkle-button:hover .sparkle-icon {
    transform: rotate(180deg) scale(1.1);
}

.sparkle-button:hover .sparkle-icon::before,
.sparkle-button:hover .sparkle-icon::after {
    background-color: white;
}

@keyframes curveToCircle {
    0% {
        border-radius: 0%;
        background: transparent;
    }
    30% {
        border-radius: 20% 80% 30% 70%;
        background: #8FA7F7;
    }
    60% {
        border-radius: 70% 30% 80% 20%;
        background: #8FA7F7;
    }
    100% {
        border-radius: 50%;
        background: #8FA7F7;
    }
}

.sparkle-button:active {
    transform: none;
    box-shadow: none;
}

.sparkle-icon {
    width: 20px;
    height: 20px;
    position: relative;
    transition: transform 0.3s ease;
}

.sparkle-icon::before,
.sparkle-icon::after {content: '';position: absolute;background-color: var(--pink);transition: all 0.3s ease;}
/* Vertical diamond shape */
.sparkle-icon::before {width: 10px;height: 20px;top: 0;left: 50%;transform: translateX(-50%) rotate(0deg);clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);}
/* Horizontal diamond shape */
.sparkle-icon::after {width: 20px;height: 10px;top: 50%;left: 0;transform: translateY(-50%) rotate(0deg);clip-path: polygon(50% 0%, 100% 50%, 50% 100%, 0% 50%);}
/* Animation on click */
.sparkle-button.clicked {
    animation: none;
}

@keyframes sparkle-pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}
.demo-container {text-align: center;}
.demo-title {margin-bottom: 30px;color: #333;font-size: 18px;font-weight: 500;}
/*scrollbar css*/
.varient_swiper_wrap_cont .custom_products_radio::-webkit-scrollbar-track { border-radius: 10px;}
.varient_swiper_wrap_cont .custom_products_radio::-webkit-scrollbar-thumb {background: #C12A6D; border-radius: 10px;}
.varient_swiper_wrap_cont .custom_products_radio::-webkit-scrollbar-thumb:hover {background: #C12A6D; }
.varient_swiper_wrap_cont .custom_products_radio{scrollbar-width: auto;scrollbar-color: auto;}
/**/
#kt_body .varaint_options .storage-option{padding:5px;}
.varaint_options{max-height:150px;overflow-y:auto;}
.varaint_options::-webkit-scrollbar-track { border-radius: 10px;}
.varaint_options::-webkit-scrollbar-thumb {background: #C12A6D; border-radius: 10px;}
.varaint_options::-webkit-scrollbar-thumb:hover {background: #C12A6D; }
.varaint_options{scrollbar-width: auto;scrollbar-color: auto;}
.view_more_btn_load button{margin:auto;display:flex;align-items:center;justify-content:center;gap:5px;border-radius: 30px;}
.view_more_btn_load button i{color:#fff;font-size:14px;}
.view_more_btn_load{margin-top: 40px;}
.add_tag_btn_product_det_pg{display:flex;align-items:center;justify-content:space-between;column-gap: 10px;}
.product_detail_sec .add_tag_btn_product_det_pg .quanitity_counter{margin-top:0px;}
.product_detail_sec .add_tag_btn_product_det_pg button.add_to_cart_button.btn_pink{flex:2;width:unset;}
.add_tag_btn_product_det_pg .add_to_crt_counter_plus{padding-top:0px }
.add_tag_btn.add_tag_btn_product_det_pg .quanitity_counter .add_to_crt_counter_plus .counter_product_wrap .counter_product_wrap_input{width:100%;}
div:hover, main:hover, ol:hover, pre:hover, span:hover, ul:hover {
    scrollbar-color: #C12A6D transparent;
}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .part_shirt_heading p:has(img) {width: 100%;height: 100px;display: flex;gap: 20px;flex-wrap: wrap}
.classic_black_grey_stripe_shirt .classic_black_shirts .product_description .shirts_long_desc .part_shirt_heading p:has(img) img{height:100%}
